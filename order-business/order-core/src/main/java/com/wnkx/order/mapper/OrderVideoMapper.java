package com.wnkx.order.mapper;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.ruoyi.common.core.enums.OrderStatusEnum;
import com.ruoyi.common.security.utils.SecurityUtils;
import com.ruoyi.system.api.domain.dto.OrderVideoStatisticsDTO;
import com.ruoyi.system.api.domain.dto.order.*;
import com.ruoyi.system.api.domain.dto.order.finance.FinancialVerificationExportDTO;
import com.ruoyi.system.api.domain.entity.biz.datastatistics.ModelOrderRankingInfo;
import com.ruoyi.system.api.domain.entity.order.OrderVideo;
import com.ruoyi.system.api.domain.entity.order.OrderVideoDataStatisticsDay;
import com.ruoyi.system.api.domain.entity.order.OrderVideoMatch;
import com.ruoyi.system.api.domain.entity.order.OrderVideoModelChange;
import com.ruoyi.system.api.domain.entity.order.datastatistics.CustomerServiceAddedCompleteCountInfo;
import com.ruoyi.system.api.domain.vo.OrderVideoStatisticsDetailVO;
import com.ruoyi.system.api.domain.vo.OrderVideoStatisticsVO;
import com.ruoyi.system.api.domain.vo.biz.business.BusinessAccountVO;
import com.ruoyi.system.api.domain.vo.order.*;
import com.ruoyi.system.api.domain.vo.order.datastatistics.OrderVideoBaseBoardVO;
import com.ruoyi.system.api.domain.vo.order.finace.FinancialVerificationAllExportVO;
import com.ruoyi.system.api.domain.vo.order.finace.OrderPayVideoDetailVO;
import com.ruoyi.system.api.domain.vo.order.workbench.PauseMatchVO;
import com.wnkx.db.mapper.SuperMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 订单_视频Mapper接口
 *
 * <AUTHOR>
 * @date 2024-05-30
 */
@Mapper
public interface OrderVideoMapper extends SuperMapper<OrderVideo> {


    /**
     * 获取工作台统计信息
     * @param dto
     * @return
     */
    WorkbenchVO getWorkbenchStatisticsVO(@Param("dto")WorkbenchDTO dto);


    /**
     * 获取物流数量
     * @param dto
     * @return
     */
    Integer getLogisticCount(@Param("dto") LogisticCountDTO dto);

    /**
     * 查询订单_视频列表
     *
     * @param orderListDTO 订单_视频
     * @return 订单_视频集合
     */
    public List<OrderVideoVO> selectOrderVideoListByCondition(@Param("dto") OrderListDTO orderListDTO);

    /**
     * 获取账号订单列表
     * @return
     */
    List<OrderVideoVO> selectAccountOrderList(@Param("dto") AccountOrderListDTO dto);


    /**
     * 获取中文部待审核订单列表（5条）
     * @param userId
     * @return
     */
    List<OrderVideoVO> selectChineseUnConfirmList(@Param("userId") Long userId);

    /**
     * 获取英文部待匹配订单列表（5条）
     * @param modelIds
     * @param matchStatus
     * @return
     */
    List<OrderVideoVO> selectEnglishUnMatchList(@Param("modelIds") List<Long> modelIds, @Param("matchStatus") Integer matchStatus);

    /**
     * 查询英文部-交易关闭订单（需发货、待完成）
     * @param userId
     * @return
     */
    List<OrderVideoVO> selectEnglishCloseList(@Param("userId") Long userId);

    /**
     * 英文暂停匹配列表
     * @param modelIds
     * @return
     */
    List<PauseMatchVO> selectEnglishPauseMatchList(@Param("modelIds") List<Long> modelIds);


    /**
     * 获取可退款金额
     *
     * @param videoId
     */
    default BigDecimal getRefundAmount(Long videoId) {
        OrderVideo orderVideo = this.selectById(videoId);
        return orderVideo.getPayAmount();
    }

    /**
     * 获取订单统计数据
     *
     * @param dto
     * @return
     */
    OrderVideoStatisticsVO orderVideoStatistics(@Param("dto") OrderVideoStatisticsDTO dto);


    /**
     * 获取订单统计详情数据
     *
     * @param dto
     * @return
     */
    List<OrderVideoStatisticsDetailVO> orderVideoStatisticsDetail(@Param("dto") OrderVideoStatisticsDTO dto);


    /**
     * 当
     * 1、选定的模特无法接单
     * 2、更改了拍摄模特
     * 3、更改了选定模特
     * 时 清除视频订单的[标记订单]的相关字段 以及 清除 视频订单的携带订单相关
     */
    void clearFlagOrder(@Param("videoIds") List<Long> videoIds);

    /**
     * 商家端-订单各个状态统计
     */
    OrderStatusVO merchantOrderStatusCount(@Param("merchantId") Long merchantId, @Param("statusMap") Map<String, Integer> statusMap);


    /**
     * 商家端-工作台-订单各个状态统计
     * @param merchantId
     * @param createOrderUserAccount
     * @param statusMap
     * @return
     */
    OrderStatusVO workbenchOrderStatusCount(@Param("merchantId") Long merchantId,@Param("createOrderUserAccount") String createOrderUserAccount, @Param("statusMap") Map<String, Integer> statusMap);

    /**
     * 运营端-订单各个状态统计
     */
    OrderStatusVO backOrderStatusCount(@Param("statusMap") Map<String, Integer> statusMap);

    /**
     * 获取正常状态的视频订单
     *
     * @param orderNum
     * @return
     */
    default List<OrderVideo> getNormalVideoList(String orderNum) {
        return this.selectList(new LambdaQueryWrapper<OrderVideo>()
                .eq(OrderVideo::getOrderNum, orderNum)
                .notIn(OrderVideo::getStatus, OrderStatusEnum.UN_PAY.getCode(), OrderStatusEnum.UN_CHECK.getCode(), OrderStatusEnum.TRADE_CLOSE.getCode()));
    }

    /**
     * 通过订单编号批量更新视频订单状态
     *
     * @param orderNums 订单编号
     * @param status    更新的状态
     */
    default void updateStatusByOrderNum(List<String> orderNums, Integer status) {
        this.update(null, new LambdaUpdateWrapper<OrderVideo>()
                .in(OrderVideo::getOrderNum, orderNums)
                .set(OrderVideo::getStatus, status));
    }

    /**
     * 通过订单编号获取视频订单
     */
    default List<OrderVideo> selectByOrderNum(String orderNum) {
        return this.selectList(new LambdaQueryWrapper<OrderVideo>()
                .eq(OrderVideo::getOrderNum, orderNum));
    }

    /**
     * 通过订单编号获取视频订单*
     *
     * @param orderNums
     * @return
     */
    default List<OrderVideo> selectByOrderNums(Collection<String> orderNums) {
        return this.selectList(new LambdaQueryWrapper<OrderVideo>()
                .in(OrderVideo::getOrderNum, orderNums)
                .orderByAsc(OrderVideo::getCreateTime)
                .orderByAsc(OrderVideo::getId)
        );
    }

    /**
     * 通过订单编号获取视频订单*
     *
     * @param orderNums
     * @return
     */
    default List<OrderVideo> selectByOrderNumsAsc(Collection<String> orderNums) {
        return this.selectList(new LambdaQueryWrapper<OrderVideo>()
                .in(OrderVideo::getOrderNum, orderNums)
                .orderByAsc(OrderVideo::getId)
        );
    }

    /**
     * 通过订单编号获取对接人为空的视频订单
     */
    default List<OrderVideo> selectByOrderNumsAndContactIsNull(Collection<String> orderNums) {
        return this.selectList(new LambdaQueryWrapper<OrderVideo>()
                .in(OrderVideo::getOrderNum, orderNums)
                .isNull(OrderVideo::getContactId)
        );
    }

    /**
     * 根据订单号列表获取未完成订单
     *
     * @param orderNums
     * @return
     */
    default List<OrderVideo> selectUnFinishOrderByOrderNums(Collection<String> orderNums) {
        return this.selectList(new LambdaQueryWrapper<OrderVideo>()
                .in(OrderVideo::getOrderNum, orderNums)
                .in(OrderVideo::getStatus, List.of(
                        OrderStatusEnum.UN_PAY.getCode(),
                        OrderStatusEnum.UN_CHECK.getCode(),
                        OrderStatusEnum.UN_CONFIRM.getCode(),
                        OrderStatusEnum.UN_MATCH.getCode(),
                        OrderStatusEnum.NEED_FILLED.getCode(),
                        OrderStatusEnum.UN_FINISHED.getCode(),
                        OrderStatusEnum.NEED_CONFIRM.getCode()
                ))
        );
    }


    /**
     * 获取商户视频订单数量
     *
     * @param merchantCode 商户code
     * @return 视频订单数量
     */
    int getOrderVideoCount(@Param("merchantCode") String merchantCode, @Param("ignoreStatus") List<Integer> ignoreStatus);


    /**
     * 获取商家照顾单数量
     *
     * @param merchantCode
     * @return
     */
    int getOrderVideoCareCount(@Param("merchantCode") String merchantCode);

    /**
     * 根据条件查询视频订单列表
     */
    default List<OrderVideo> selectOrderVideoListByConditionV2(OrderVideoListDTO orderVideoListDTO) {
        return this.selectList(new LambdaQueryWrapper<OrderVideo>()
                .in(CollUtil.isNotEmpty(orderVideoListDTO.getStatus()), OrderVideo::getStatus, orderVideoListDTO.getStatus())
                .like(StrUtil.isNotBlank(orderVideoListDTO.getVideoCode()), OrderVideo::getVideoCode, orderVideoListDTO.getVideoCode())
                .orderByDesc(OrderVideo::getCreateTime));
    }

    /**
     * 获取视频订单的对接人id
     */
    default Set<Long> getOrderContactId() {
        List<OrderVideo> orderVideos = this.selectList(new LambdaQueryWrapper<OrderVideo>()
                .select(OrderVideo::getContactId)
                .isNotNull(OrderVideo::getContactId)
        );
        return orderVideos.stream().map(OrderVideo::getContactId).collect(Collectors.toSet());
    }

    /**
     * 获取视频订单的出单人id
     */
    default Set<Long> getOrderIssueId() {
        List<OrderVideo> orderVideos = this.selectList(new LambdaQueryWrapper<OrderVideo>()
                .select(OrderVideo::getIssueId)
                .isNotNull(OrderVideo::getIssueId)
        );
        return orderVideos.stream().map(OrderVideo::getIssueId).collect(Collectors.toSet());
    }

    /**
     * 获取视频订单的拍摄模特id
     */
    default Set<Long> getOrderShootModelId() {
        List<OrderVideo> orderVideos = this.selectList(new LambdaQueryWrapper<OrderVideo>()
                .select(OrderVideo::getShootModelId)
                .isNotNull(OrderVideo::getShootModelId)
        );
        return orderVideos.stream().map(OrderVideo::getShootModelId).collect(Collectors.toSet());
    }

    /**
     * 校验admin数据权限
     *
     * @return true 校验通过 false 校验不通过
     */
    default Boolean checkAdminDataScope(List<Long> videoIds) {
        return !exists(new LambdaQueryWrapper<OrderVideo>()
                .in(OrderVideo::getId, videoIds)
                .and(w -> w.isNull(OrderVideo::getContactId).isNull(OrderVideo::getIssueId))
        );
    }

    /**
     * 校验运营数据权限
     *
     * @return true 校验不通过 false 校验通过
     */
    int checkManagerDataScope(@Param("dto") CheckDataScopeDTO dto, @Param("userid") Long userid);

    /**
     * 校验商家数据权限
     *
     * @return true 校验不通过 false 校验通过
     */
    int checkUserDataScope(@Param("dto") CheckDataScopeDTO dto, @Param("businessAccount") BusinessAccountVO businessAccount);

    /**
     * 获取 待拍数、待确认数、待确认订单数（即运营提交预选模特给商家后 待商家确认的订单数（即需发货订单））、已完成订单数
     */
    List<ModelOrderVO> getModelOrderCount(@Param("modelIds") Collection<Long> modelIds);

    /**
     * 获取下单用户id
     *
     * @return
     */
    Set<Long> getOrderUserId();

    /**
     * 获取视频订单当前信息（用于比较变更后的数据）
     */
    OrderVideoCurrentInfoVO getOrderVideoCurrentInfo(Long id);

    List<OrderVideo> selectListByProductPicIsNullAndProductLink(@Param("productLinks") Set<String> productLinks);

    /**
     * * 获取 订单收支明细 - 视频明细
     *
     * @param dto
     * @return
     */
    List<OrderPayVideoDetailVO> orderPayDetailVideoList(OrderPayDetailVideoListDTO dto);

    /**
     * 获取财务对账-视频明细列表数据
     * @param dto
     * @return
     */
    List<FinancialVerificationAllExportVO> financialVerificationAllExportList(FinancialVerificationExportDTO dto);

    /**
     * 将参考图片置空
     *
     * @param id
     */
    default void setReferencePicNull(Long id) {
        this.update(null, new LambdaUpdateWrapper<OrderVideo>()
                .set(OrderVideo::getReferencePicId, null)
                .eq(OrderVideo::getId, id));

    }

    /**
     * 更新视频订单
     */
    void updateOrderVideo(OrderVideo orderVideo);


    /**
     * 设置视频订单自动完成时间
     */
    default void setAutoCompleteTime(Long videoId, DateTime autoCompleteTime) {
        update(null, new LambdaUpdateWrapper<OrderVideo>()
                .eq(OrderVideo::getId, videoId)
                .set(OrderVideo::getAutoCompleteTime, autoCompleteTime)
        );
    }

    /**
     * 查询需要修改对接人的视频订单
     */
    void updateIssueId(@Param("dto") UpdateIssueIdDTO dto);

    /**
     * 设置订单号使用余额为0
     */
    default void setBalanceZeroByOrderNum(List<String> orderNums) {
        this.update(null, new LambdaUpdateWrapper<OrderVideo>()
                .set(OrderVideo::getUseBalance, BigDecimal.ZERO)
                .in(OrderVideo::getOrderNum, orderNums));

    }

    default List<String> selectOrderUserNicknameList(String name) {
        return this.selectList(new LambdaQueryWrapper<OrderVideo>()
                .eq(OrderVideo::getCreateOrderBusinessId, SecurityUtils.getLoginBusinessUser().getBusinessAccountVO().getBusinessId())
                .like(StrUtil.isNotBlank(name), OrderVideo::getCreateOrderUserNickName, name)
                .groupBy(OrderVideo::getCreateOrderUserNickName)
                .select(OrderVideo::getCreateOrderUserNickName)
        ).stream().map(OrderVideo::getCreateOrderUserNickName).collect(Collectors.toList());
    }

    /**
     * 获取待完成、需确认状态下的订单的出单人信息
     */
    default List<OrderVideo> getUnFinishedAndNeedConfirmOrderIssueId() {
        return selectList(new LambdaQueryWrapper<OrderVideo>()
                .select(OrderVideo::getId, OrderVideo::getVideoCode, OrderVideo::getIssueId)
                .isNotNull(OrderVideo::getIssueId)
                .in(OrderVideo::getStatus, OrderStatusEnum.UN_FINISHED.getCode(), OrderStatusEnum.NEED_CONFIRM.getCode())
        );
    }

    /**
     * 订单列表-获取下单用户下拉框（商家端）
     */
    default List<OrderVideo> companyOrderUserSelect(Long businessId, String keyword) {
        return selectList(new LambdaQueryWrapper<OrderVideo>()
                .eq(OrderVideo::getCreateOrderBusinessId, businessId)
                .isNotNull(OrderVideo::getCreateOrderBusinessId)
                .like(CharSequenceUtil.isNotBlank(keyword), OrderVideo::getCreateOrderUserName, keyword)
                .select(OrderVideo::getCreateOrderUserName)
        );
    }

    /**
     * 更换模特
     */
    default void changeModel(OrderVideo orderVideo) {
        update(null, new LambdaUpdateWrapper<OrderVideo>()
                .eq(OrderVideo::getId, orderVideo.getId())
                .set(OrderVideo::getIntentionModelId, orderVideo.getIntentionModelId())
                .set(OrderVideo::getStatus, OrderStatusEnum.UN_CONFIRM.getCode())
                .set(OrderVideo::getStatusTime, DateUtil.date())
                .set(OrderVideo::getShootModelId, null)
                .set(OrderVideo::getIssueId, null)
        );
    }

    /**
     * 更新视频订单 若字段为null 更新为null
     * PS:请注意字段值
     */
    void updateOrderVideoFieldNullToNull(@Param("orderVideo") OrderVideo orderVideo);

    /**
     * 通过视频编码获取视频订单列表
     */
    default List<OrderVideo> selectListByVideoCodes(List<String> videoCodes) {
        return selectList(new LambdaQueryWrapper<OrderVideo>()
                .in(OrderVideo::getVideoCode, videoCodes)
        );
    }
    /**
     * 通过视频编码获取视频订单
     */
    default OrderVideo selectOneByVideoCode(String videoCode){
        return selectOne(new LambdaQueryWrapper<OrderVideo>()
                .eq(OrderVideo::getVideoCode, videoCode)
        );
    }
    /**
     * （一次性）补全order_video_model_change的选定信息
     */
    List<OrderVideoModelChange> test();

    /**
     * （一次性）补全order_video_match的选定信息
     */
    List<OrderVideoMatch> test2();

    /**
     * (一次性)更新order_video_model_change数据
     */
    List<OrderVideoModelChange> test3();

    /**
     * 通过商家ID查询商家是否有进行中或者交易完成的视频订单
     */
    Boolean hasValidVideoOrderByBusinessId(@Param("businessId") Long businessId, @Param("startTime") String startTime);

    /**
     * 模特数据统计-模特接单排行榜
     */
    List<ModelOrderRankingInfo> getModelOrderRanking(@Param("date") String date);

    /**
     * 通过日期 获取中文部客服新增/完成订单数量
     */
    List<CustomerServiceAddedCompleteCountInfo> getChineseCustomerServiceOrderCountByDate(@Param("date") String date);

    /**
     * 通过日期 获取英文部客服新增/完成订单数量
     */
    List<CustomerServiceAddedCompleteCountInfo> getEnglishCustomerServiceOrderCountByDate(@Param("date") String date);

    /**
     * 订单列表-获取订单运营下拉框（运营端）
     */
    List<OrderVideo> backCreateOrderUserNameSelect(@Param("keyword") String keyword);


    /**
     * 视频订单数据统计-按天统计视频订单数据
     */
    OrderVideoDataStatisticsDay getOrderVideoDataStatisticsDay(@Param("date") String date);

    /**
     * 视频订单数据-基础看板
     */
    OrderVideoBaseBoardVO getOrderVideoBaseBoard();

    /**
     * 视频订单列表-获取意向模特下拉框
     */
    default Collection<Long> orderIntentionModelSelect() {
        List<OrderVideo> orderVideos = selectList(new LambdaQueryWrapper<OrderVideo>()
                .eq(OrderVideo::getStatus, OrderStatusEnum.UN_MATCH.getCode())
                .isNotNull(OrderVideo::getIntentionModelId)
                .select(OrderVideo::getIntentionModelId)
        );
        return orderVideos.stream().map(OrderVideo::getIntentionModelId).collect(Collectors.toSet());
    }

    /**
     * 通过拍摄模特ID查询视频订单
     */
    default List<OrderVideo> selectListByShootModelIds(Collection<Long> modelIds) {
        return selectList(new LambdaQueryWrapper<OrderVideo>()
                .in(OrderVideo::getShootModelId, modelIds)
        );
    }

    default Date getOrderFirstMatchTime(Long orderId){
        return selectOne(new LambdaQueryWrapper<OrderVideo>()
                .eq(OrderVideo::getId, orderId)
                .select(OrderVideo::getFirstMatchTime)
                .last("limit 1")
        ).getFirstMatchTime();
    }
}
