package com.wnkx.order.mapper;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ruoyi.system.api.domain.entity.order.OrderVideoModelChange;
import com.ruoyi.system.api.domain.vo.order.OrderVideoModelChangeVO;
import com.wnkx.db.mapper.SuperMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/10/15 11:55
 */
@Mapper
public interface OrderVideoModelChangeMapper extends SuperMapper<OrderVideoModelChange> {

    /**
     * 查询模特变更记录
     */
    List<OrderVideoModelChangeVO> selectOrderVideoModelChangeListByVideoId(@Param("videoId") Long videoId);

    /**
     * 查询模特变更记录
     * @param videoId
     * @param modelId
     * @return
     */
    default List<OrderVideoModelChange> selectOrderVideoModelChangeListByVideoIdAndModelId(Long videoId, Long modelId){
        return selectList(new LambdaQueryWrapper<OrderVideoModelChange>()
                .eq(OrderVideoModelChange::getVideoId, videoId)
                .eq(OrderVideoModelChange::getModelId, modelId)
        );
    }


    /**
     * 更新视频订单 若字段为null 更新为null
     * PS:请注意字段值
     */
    void updateOrderVideoModelChangeFieldNullToNull(@Param("orderVideoModelChange") OrderVideoModelChange orderVideoModelChange);
}
