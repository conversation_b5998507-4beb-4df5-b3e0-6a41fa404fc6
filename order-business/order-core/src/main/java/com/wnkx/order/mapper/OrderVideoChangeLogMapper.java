package com.wnkx.order.mapper;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ruoyi.common.core.enums.ChangeLogTypeEnum;
import com.ruoyi.system.api.domain.entity.order.OrderVideoChangeLog;
import com.ruoyi.system.api.domain.vo.order.OrderVideoChangeLogVO;
import com.wnkx.db.mapper.SuperMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/8/28 11:14
 */
@Mapper
public interface OrderVideoChangeLogMapper extends SuperMapper<OrderVideoChangeLog> {

    /**
     * 根据视频订单id查询变更记录
     */
    List<OrderVideoChangeLogVO> selectVideoChangeListByVideoId(@Param("videoId") Long videoId);

    /**
     * 查询视频订单是否有变更记录
     */
    default boolean videoHasChangeLog(Long videoId) {
        return exists(new LambdaQueryWrapper<OrderVideoChangeLog>()
                .eq(OrderVideoChangeLog::getVideoId, videoId)
                .ne(OrderVideoChangeLog::getLogType, ChangeLogTypeEnum.INIT_LOG.getCode())
        );
    }
}
