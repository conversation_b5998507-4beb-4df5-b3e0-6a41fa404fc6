<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wnkx.order.mapper.OrderVideoChangeLogMapper">


    <select id="selectVideoChangeListByVideoId"
            resultType="com.ruoyi.system.api.domain.vo.order.OrderVideoChangeLogVO">
        SELECT
            ovcl.id,
            ovcl.video_id,
            ovcl.rollback_id,
            ovcl.change_time,
            ovcl.change_user_id,
            ovcl.change_type,
            ovcl.log_type,
            CASE WHEN EXISTS (
                SELECT 1
                FROM order_video_match_preselect_model ovmpm
                WHERE ovmpm.match_id = prevovm.id
                  AND ovmpm.oust_type = 1
            ) THEN 1 ELSE 0 END AS isRejectAfterSubmitModel
        FROM
            order_video_change_log ovcl
            LEFT JOIN order_video_match AS curovm ON curovm.video_id = ovcl.video_id
                AND curovm.rollback_id &lt;=> ovcl.rollback_id
                AND ABS( TIMESTAMPDIFF( SECOND, curovm.start_time, ovcl.change_time )) &lt;= 3
                AND ovcl.log_type = 4
            LEFT JOIN LATERAL (
                SELECT
                    p.id
                FROM
                    order_video_match AS p
                WHERE
                    p.video_id = curovm.video_id
                  AND p.rollback_id &lt;=> curovm.rollback_id
                  AND p.count &lt; curovm.count
                  AND p.status = 1
                ORDER BY p.count DESC
                LIMIT 1
                ) AS prevovm ON TRUE
        WHERE
            ovcl.video_id = #{videoId}
        ORDER BY
            ovcl.change_time DESC,
            ovcl.log_type DESC
    </select>
</mapper>