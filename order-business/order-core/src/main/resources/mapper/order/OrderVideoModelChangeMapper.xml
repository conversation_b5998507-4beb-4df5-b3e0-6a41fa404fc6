<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wnkx.order.mapper.OrderVideoModelChangeMapper">


    <update id="updateOrderVideoModelChangeFieldNullToNull">
        UPDATE order_video_model_change
        SET
            video_id = #{orderVideoModelChange.videoId},
            rollback_id = #{orderVideoModelChange.rollbackId},
            model_id = #{orderVideoModelChange.modelId},
            source = #{orderVideoModelChange.source},
            model_pic = #{orderVideoModelChange.modelPic},
            name = #{orderVideoModelChange.name},
            nation = #{orderVideoModelChange.nation},
            sex = #{orderVideoModelChange.sex},
            type = #{orderVideoModelChange.type},
            age_group = #{orderVideoModelChange.ageGroup},
            platform = #{orderVideoModelChange.platform},
            cooperation = #{orderVideoModelChange.cooperation},
            cooperation_score = #{orderVideoModelChange.cooperationScore},
            issue_user_name = #{orderVideoModelChange.issueUserName},
            selected_time = #{orderVideoModelChange.selectedTime},
            schedule_type = #{orderVideoModelChange.scheduleType},
            carry_type = #{orderVideoModelChange.carryType},
            commission_unit = #{orderVideoModelChange.commissionUnit},
            commission = #{orderVideoModelChange.commission},
            overstatement = #{orderVideoModelChange.overstatement},
            specialty_category = #{orderVideoModelChange.specialtyCategory},
            model_tag = #{orderVideoModelChange.modelTag},
            create_time = #{orderVideoModelChange.createTime}
        WHERE id = #{orderVideoModelChange.id}
    </update>
    <select id="selectOrderVideoModelChangeListByVideoId"
            resultType="com.ruoyi.system.api.domain.vo.order.OrderVideoModelChangeVO">
        SELECT
            ovmc.id,
            ovmc.rollback_id,
            ovmc.source,
            ovmc.model_pic,
            ovmc.name,
            ovmc.nation,
            ovmc.sex,
            ovmc.type,
            ovmc.age_group,
            ovmc.platform,
            ovmc.cooperation,
            ovmc.cooperation_score,
            ovmc.issue_user_name,
            ovmc.selected_time,
            ovmc.schedule_type,
            ovmc.carry_type,
            ovmc.commission_unit,
            ovmc.commission,
            ovmc.overstatement,
            ovmc.specialty_category,
            ovmc.model_tag,
            ovmc.create_time,
            CASE WHEN EXISTS (
                SELECT 1
                FROM order_video_match_preselect_model ovmpm
                WHERE ovmpm.match_id = CASE WHEN ovmc.source = 2 THEN prevovm.id ELSE curovm.id END
                    AND ovmpm.oust_type = 1
                ) THEN 1 ELSE 0 END AS isRejectAfterSubmitModel
        FROM order_video_model_change AS ovmc
            LEFT JOIN LATERAL (
                SELECT
                    ovmc2.selected_time
                FROM
                    order_video_model_change ovmc2
                WHERE
                    ovmc2.video_id = ovmc.video_id
                    AND ovmc2.rollback_id &lt;=> ovmc.rollback_id
                    AND ovmc2.source = 2
                    AND ovmc2.id &lt; ovmc.id
                ORDER BY ovmc2.id DESC
                LIMIT 1
            ) AS prev2 ON ovmc.source = 1
            LEFT JOIN order_video_match AS curovm ON curovm.video_id = ovmc.video_id AND curovm.rollback_id &lt;=> ovmc.rollback_id AND curovm.submit_time  = CASE WHEN ovmc.source = 2 THEN ovmc.selected_time ELSE prev2.selected_time END
            LEFT JOIN LATERAL (
            SELECT
                p.id
            FROM
                order_video_match AS p
            WHERE
                p.video_id = curovm.video_id
              AND p.rollback_id &lt;=> curovm.rollback_id
              AND p.count &lt; curovm.count
              AND p.status = 1
            ORDER BY p.count DESC
            LIMIT 1
            ) AS prevovm ON ovmc.source = 2
        WHERE ovmc.video_id = #{videoId}
    </select>
</mapper>