package com.ruoyi.system.api.domain.dto.order;

import com.ruoyi.common.core.enums.OrderPoolPreselectedTimeEnum;
import com.ruoyi.common.core.validated.annotation.EnumValid;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/7/21 11:47
 */
@Data
public class MyPreselectFailListDTO implements Serializable {
    private static final long serialVersionUID = 5951860781592621073L;

    /**
     * 关键词
     */
    @ApiModelProperty(value = "关键词")
    private String keyword;

    /**
     * 淘汰时间
     */
    @ApiModelProperty(value = "淘汰时间（1:24小时内,2:,1~2天,3:2~3天,4:3天以上）")
    @EnumValid(enumClass = OrderPoolPreselectedTimeEnum.class, message = "[淘汰时间]输入错误")
    private List<Integer> oustTimes;

    /**
     * 照顾单
     */
    @ApiModelProperty(value = "照顾单（0=否,1=是）")
    private List<Integer> isCares;

    /**
     * 添加方式（1:意向模特,2:模特自选,3:运营添加,4:客服分发）
     */
    @ApiModelProperty(value = "添加方式（1:意向模特,2:模特自选,3:运营添加,4:客服分发）")
    private Integer addType;
}
