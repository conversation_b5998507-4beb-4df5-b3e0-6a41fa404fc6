package com.ruoyi.system.api.domain.vo.order;

import cn.hutool.core.date.DatePattern;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.ruoyi.common.core.domain.vo.UserVO;
import com.ruoyi.system.api.domain.entity.order.OrderVideoContent;
import com.ruoyi.system.api.domain.vo.biz.model.ModelOrderSimpleVO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/11/18 9:55
 */
@Data
public class OrderPoolListVO implements Serializable {

    private static final long serialVersionUID = 2882683820078581892L;
    /**
     * 主键
     */
    @ApiModelProperty(value = "主键")
    private Long id;

    /**
     * 视频订单ID
     */
    @ApiModelProperty(value = "视频订单ID")
    private Long videoId;

    /**
     * 回退ID (FK:order_video_rollback_record.id)
     */
    @ApiModelProperty("回退ID")
    private Long rollbackId;

    /**
     * 产品图变更(1:变更了,0:未变更)
     */
    @ApiModelProperty(value = "产品图变更(1:变更了,0:未变更)")
    private Boolean productPicChange;

    /**
     * 商品信息变更(1:变更了,0:未变更)
     */
    @ApiModelProperty(value = "商品信息变更(1:变更了,0:未变更)")
    private Boolean goodsInfoChange;

    @ApiModelProperty(value = "照片数量变更(1:变更了,0:未变更)")
    private Boolean picCountChange;

    @ApiModelProperty(value = "意向模特变更(1:变更了,0:未变更)")
    private Boolean intentionModelChange;

    /**
     * 拍摄建议变更(1:变更了,0:未变更)
     */
    @ApiModelProperty(value = "拍摄建议变更(1:变更了,0:未变更)")
    private Boolean shootRequiredChange;

    @ApiModelProperty(value = "产品卖点变更(1:变更了,0:未变更)")
    private Boolean sellingPointProductChange;

    @ApiModelProperty(value = "商品规格要求变更(1:变更了,0:未变更)")
    private Boolean orderSpecificationRequireChange;

    @ApiModelProperty(value = "特别强调变更(1:变更了,0:未变更)")
    private Boolean particularEmphasisChange;

    /**
     * 注意事项变更(1:变更了,0:未变更)
     */
    @ApiModelProperty(value = "注意事项变更(1:变更了,0:未变更)")
    private Boolean cautionsChange;

    /**
     * 产品图URI
     */
    @ApiModelProperty(value = "产品图URI")
    private String productPic;


    @ApiModelProperty(value = "是否照顾单（0=否,1=是）")
    private Integer isCare;

    /**
     * 视频编码
     */
    @ApiModelProperty(value = "视频编码")
    private String videoCode;

    /**
     * 产品中文名
     */
    @ApiModelProperty(value = "产品中文名")
    private String productChinese;

    /**
     * 产品英文名
     */
    @ApiModelProperty(value = "产品英文名")
    private String productEnglish;

    /**
     * 产品链接
     */
    @ApiModelProperty(value = "产品链接")
    private String productLink;

    /**
     * 参考视频链接
     */
    @ApiModelProperty(value = "参考视频链接")
    private String referenceVideoLink;

    /**
     * 使用平台(0:Amazon,1:tiktok,2:其他)
     */
    @ApiModelProperty(value = "使用平台(0:Amazon,1:tiktok,2:其他,3:APP/解说类)")
    private Integer platform;

    @ApiModelProperty(value = "视频风格(0:Amazon,1:tiktok,2:APP/解说类)")
    private Integer videoStyle;

    /**
     * 拍摄国家（1:英国,2:加拿大,3:德国,4:法国,5:意大利,6:西班牙,7:美国）
     */
    @ApiModelProperty(value = "拍摄国家（1:英国,2:加拿大,3:德国,4:法国,5:意大利,6:西班牙,7:美国）")
    private Integer shootingCountry;

    /**
     * 模特类型（0:影响者,1:素人）
     */
    @ApiModelProperty(value = "模特类型（0:影响者,1:素人）")
    private Integer modelType;

    /**
     * 视频格式（1:横屏16：9,2:竖屏9：16）
     */
    @ApiModelProperty(value = "视频格式（1:横屏16：9,2:竖屏9：16）")
    private Integer videoFormat;

    /**
     * 照片数量（1:2张/$10,2:5张/$20）
     */
    @ApiModelProperty(value = "照片数量（1:2张/$10,2:5张/$20）")
    private Integer picCount;

    @ApiModelProperty(value = "退款照片数量")
    private Integer refundPicCount;

    @ApiModelProperty(value = "剩余退款照片数量")
    private Integer surplusPicCount;

    /**
     * 参考图片（关联资源id）
     */
    @ApiModelProperty(value = "参考图片（关联资源id）")
    @JsonIgnore
    private String referencePicId;

    /**
     * 参考图片
     */
    @ApiModelProperty(value = "参考图片")
    private List<String> referencePic = new ArrayList<>();

    /**
     * 意向模特id
     */
    @ApiModelProperty(value = "意向模特id")
    @JsonIgnore
    private Long intentionModelId;

    /**
     * 意向模特id
     */
    @ApiModelProperty(value = "意向模特id")
    private ModelOrderSimpleVO intentionModel;
    /**
     * 拍摄模特id
     */
    @ApiModelProperty(value = "拍摄模特id")
    @JsonIgnore
    private Long shootModelId;

    /**
     * 拍摄模特
     */
    @ApiModelProperty(value = "拍摄模特")
    private ModelOrderSimpleVO shootModel;

    /**
     * 拍摄建议（原拍摄要求）
     */
    @ApiModelProperty("拍摄建议（原拍摄要求）")
    private List<OrderVideoContent> shootRequired;

    /**
     * 模特要求（原匹配模特注意事项）
     */
    @ApiModelProperty("模特要求（原匹配模特注意事项）")
    private OrderVideoCautionsVO orderVideoCautionsVO;

    @ApiModelProperty(value = "商品规格要求")
    private String orderSpecificationRequire;

    @ApiModelProperty(value = "特别强调")
    private String particularEmphasis;

    @ApiModelProperty(value = "特别强调图片")
    private List<String> particularEmphasisPic;


    @ApiModelProperty(value = "产品卖点")
    private String sellingPointProduct;

    /**
     * 对接人id
     */
    @ApiModelProperty(value = "对接人id")
    @JsonIgnore
    private Long contactId;

    /**
     * 对接人
     */
    @ApiModelProperty(value = "对接人")
    private UserVO contact;

    /**
     * 出单人id
     */
    @ApiModelProperty(value = "出单人id")
    @JsonIgnore
    private Long issueId;

    /**
     * 出单人
     */
    @ApiModelProperty(value = "出单人")
    private UserVO issue;

    /**
     * 匹配次数
     */
    @ApiModelProperty(value = "匹配次数")
    private Integer count;

    /**
     * 商家编码
     */
    @ApiModelProperty(value = "商家编码")
    private String merchantCode;

    /**
     * 创建订单用户微信名称
     */
    @ApiModelProperty(value = "创建订单用户微信名称")
    private String createOrderUserNickName;

    /**
     * 创建订单用户名称
     */
    @ApiModelProperty(value = "创建订单用户名称")
    private String createOrderUserName;

    /**
     * 创建订单用户ID
     */
    @ApiModelProperty(value = "创建订单用户ID")
    @JsonIgnore
    private Long createOrderUserId;

    /**
     * 订单状态
     */
    @ApiModelProperty(value = "订单状态（1:待支付,2:待审核,3:待确认,4:待匹配,5:需发货,6:待完成,7:需确认,8:已完成,9:交易关闭）")
    private Integer status;

    @ApiModelProperty(value = "创建订单登录用户id")
    private Long createOrderBizUserId;

    /**
     * 预选模特数量
     */
    @ApiModelProperty(value = "预选模特数量")
    private Integer preselectModelCount;

    /**
     * 预选模特
     */
    @ApiModelProperty(value = "预选模特")
    private List<ModelOrderSimpleVO> preselectModels = new ArrayList<>();

    @ApiModelProperty(value = "预选模特列表")
    private List<OrderVideoMatchPreselectModelVO> preselectModelList;

    /**
     * 开始时间
     */
    @ApiModelProperty(value = "开始时间")
    @JsonFormat(pattern = DatePattern.NORM_DATETIME_PATTERN)
    private Date startTime;

    /**
     * 是否有备注（true：有）
     */
    @ApiModelProperty(value = "是否有备注（true：有）")
    private Boolean hasComment;

    /**
     * 视频时长
     */
    @ApiModelProperty(value = "视频时长")
    private Integer videoDuration;

    /**
     * 订单退款信息
     */
    @ApiModelProperty(value = "订单退款信息")
    private OrderVideoRefundSimpleVO orderVideoRefund;


    /**
     * 是通品（1：是，0：不是）
     */
    @ApiModelProperty(value = "是通品（1：是，0：不是）")
    private Integer isGund;

    /**
     * 是否限制为优质模特
     */
    @ApiModelProperty(value = "是否限制为优质模特")
    private Boolean isLimitedToQualityModel;

    /**
     * 是否是商家驳回后提交的模特
     */
    @ApiModelProperty(value = "是否是商家驳回后提交的模特")
    private Boolean isRejectAfterSubmitModel;
}
