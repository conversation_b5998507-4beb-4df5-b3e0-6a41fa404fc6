
package com.ruoyi.system.api.domain.vo.order;

import cn.hutool.core.date.DatePattern;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024-11-16
 */
@Data
public class OrderVideoMatchVO implements Serializable {

    private static final long serialVersionUID = 2598592587008230070L;
    /**
     * 主键
     */
    @ApiModelProperty(value = "主键")
    private Long id;

    /**
     * 视频id
     */
    @ApiModelProperty(value = "视频id")
    private Long videoId;

    /**
     * 回退ID (FK:order_video_rollback_record.id)
     */
    @ApiModelProperty("回退ID")
    private Long rollbackId;


    /**
     * 匹配次数
     */
    @ApiModelProperty(value = "匹配次数")
    private Integer count;

    /**
     * 状态（1:正常,2:暂停）
     */
    @ApiModelProperty(value = "状态（1:正常,2:暂停）")
    private Integer status;

    /**
     * 开始时间
     */
    @ApiModelProperty(value = "开始时间")
    @JsonFormat(pattern = DatePattern.NORM_DATETIME_PATTERN)
    private Date startTime;

    /**
     * 结束时间
     */
    @ApiModelProperty(value = "结束时间")
    @JsonFormat(pattern = DatePattern.NORM_DATETIME_PATTERN)
    private Date endTime;

    /**
     * 正常的预选模特列表
     */
    @ApiModelProperty(value = "正常的预选模特列表")
    private List<OrderVideoMatchPreselectModelVO> normalOrderVideoMatchPreselectModelVOS = new ArrayList<>();

    /**
     * 淘汰的预选模特列表
     */
    @ApiModelProperty(value = "淘汰的预选模特列表")
    private List<OrderVideoMatchPreselectModelVO> outOrderVideoMatchPreselectModelVOS = new ArrayList<>();

    /**
     * 是否是商家驳回后提交的模特
     */
    @ApiModelProperty(value = "是否是商家驳回后提交的模特")
    private Boolean isRejectAfterSubmitModel;
}
