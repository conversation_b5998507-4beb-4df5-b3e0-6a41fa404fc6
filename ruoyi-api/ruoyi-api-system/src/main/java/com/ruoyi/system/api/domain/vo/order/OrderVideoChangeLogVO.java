package com.ruoyi.system.api.domain.vo.order;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.ruoyi.common.core.domain.vo.UserVO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/8/28 11:05
 */
@Data
public class OrderVideoChangeLogVO implements Serializable {
    private static final long serialVersionUID = 765001373560973676L;
    /**
     * 主键
     */
    @ApiModelProperty("主键")
    private Long id;
    /**
     * 视频id (FK:order_video.id)
     */
    @ApiModelProperty("视频id")
    private Long videoId;

    /**
     * 回退ID (FK:order_video_rollback_record.id)
     */
    @ApiModelProperty("回退ID")
    private Long rollbackId;

    /**
     * 变更时间
     */
    @ApiModelProperty("变更时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date changeTime;
    /**
     * 变更操作运营
     */
    @ApiModelProperty("变更操作运营")
    @JsonIgnore
    private Long changeUserId;
    /**
     * 变更操作运营
     */
    @ApiModelProperty("变更操作运营")
    private UserVO changeUser;
    /**
     * 记录类型（1:初始记录,2:编辑订单时修改,3:商家同意后修改）
     */
    @ApiModelProperty("记录类型（1:初始记录,2:编辑订单时修改,3:商家同意后修改）")
    private Integer logType;

    /**
     * 是否是商家驳回后提交的模特
     */
    @ApiModelProperty(value = "是否是商家驳回后提交的模特")
    private Boolean isRejectAfterSubmitModel;

    /**
     * 变更记录详情
     */
    @ApiModelProperty("变更记录详情")
    private List<OrderVideoChangeLogInfoVO> changeLogInfoList;
}
