package com.ruoyi.system.api.domain.vo.order;

import cn.hutool.core.date.DatePattern;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.ruoyi.common.core.annotation.Excel;
import com.ruoyi.common.core.domain.vo.UserVO;
import com.ruoyi.system.api.domain.entity.order.OrderVideoContent;
import com.ruoyi.system.api.domain.entity.order.OrderVideoFeedBack;
import com.ruoyi.system.api.domain.vo.biz.model.ModelOrderSimpleVO;
import com.ruoyi.system.api.domain.vo.order.logistic.LogisticFollowVideoInfoVO;
import com.ruoyi.system.api.domain.vo.order.promotion.OrderDiscountDetailVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/5/31 15:10
 */
@ApiModel(value = "订单_视频对象VO")
@Data
public class OrderVideoVO implements Serializable {
    private static final long serialVersionUID = -427307053303319381L;
    /**
     * 主键
     */
    @ApiModelProperty(value = "主键")
    private Long id;

    /**
     * 订单号
     */
    @ApiModelProperty(value = "订单号")
    private String orderNum;

    /**
     * 视频编码
     */
    @ApiModelProperty(value = "视频编码")
    private String videoCode;

    @ApiModelProperty(value = "是否照顾单（0=否,1=是）")
    @Excel(name = "是否照顾单", readConverterExp = "0=否,1=是")
    @NotNull(message = "[是否照顾单]不能为空")
    private Integer isCare;

    /**
     * 订单状态
     */
    @ApiModelProperty(value = "订单状态（1:待支付,2:待审核,3:待确认,4:待匹配,5:需发货,6:待完成,7:需确认,8:已完成,9:交易关闭）")
    private Integer status;

    /**
     * 订单进入新状态的时间
     */
    @ApiModelProperty(value = "订单进入新状态的时间")
    @JsonFormat(pattern = DatePattern.NORM_DATETIME_PATTERN)
    private Date statusTime;

    /**
     * 使用平台(0:Amazon,1:tiktok,2:其他,3:APP/解说类)
     */
    @ApiModelProperty(value = "使用平台(0:Amazon,1:tiktok,2:其他,3:APP/解说类)", notes = "0:Amazon,1:tiktok,2:其他,3:APP/解说类")
    private Integer platform;

    @ApiModelProperty(value = "视频风格(0:Amazon,1:tiktok,2:APP/解说类)")
    private Integer videoStyle;

    /**
     * 产品中文名
     */
    @ApiModelProperty(value = "产品中文名")
    private String productChinese;

    /**
     * 产品英文名
     */
    @ApiModelProperty(value = "产品英文名")
    private String productEnglish;

    /**
     * 产品链接
     */
    @ApiModelProperty(value = "产品链接")
    private String productLink;

    /**
     * 产品图URI
     */
    @ApiModelProperty(value = "产品图URI")
    private String productPic;

    /**
     * 参考视频链接
     */
    @ApiModelProperty(value = "参考视频链接")
    private String referenceVideoLink;

    /**
     * 视频格式（1:横屏16：9,2:竖屏9：16）
     */
    @ApiModelProperty(value = "视频格式（1:横屏16：9,2:竖屏9：16）", notes = "1:横屏16：9,2:竖屏9：16")
    private Integer videoFormat;

    /** 视频时长 */
    @ApiModelProperty(value = "视频时长")
    private Integer videoDuration;

    /**
     * 拍摄国家（1:英国,2:加拿大,3:德国,4:法国,5:意大利,6:西班牙,7:美国）
     */
    @ApiModelProperty(value = "拍摄国家（1:英国,2:加拿大,3:德国,4:法国,5:意大利,6:西班牙,7:美国）", notes = "1:英国,2:加拿大,3:德国,4:法国,5:意大利,6:西班牙,7:美国")
    private Integer shootingCountry;

    /**
     * 模特类型（0:影响者,1:素人）
     */
    @ApiModelProperty(value = "模特类型（0:影响者,1:素人）", notes = "0:影响者,1:素人")
    private Integer modelType;

    /**
     * 实物（0:是,1:不是）
     */
    @ApiModelProperty(value = "实物（0:是,1:不是）", notes = "0:是,1:不是")
    private Integer isObject;

    /**
     * 照片数量（1:2张/$10,2:5张/$20）
     */
    @ApiModelProperty(value = "照片数量（1:2张/$10,2:5张/$20）", notes = "1:2张/$10,2:5张/$20")
    private Integer picCount;

    @ApiModelProperty(value = "退款照片数量")
    private Integer refundPicCount;

    @ApiModelProperty(value = "剩余退款照片数量")
    private Integer surplusPicCount;
    /**
     * 参考图片
     */
    @ApiModelProperty(value = "参考图片")
    private List<String> referencePic = new ArrayList<>();
    /**
     * 参考图片
     */
    @ApiModelProperty(value = "参考图片(关联资源id)")
    @JsonIgnore
    private String referencePicId;

    /**
     * 意向模特id
     */
    @ApiModelProperty(value = "意向模特id")
    @JsonIgnore
    private Long intentionModelId;

    /**
     * 意向模特
     */
    @ApiModelProperty(value = "意向模特")
    private ModelOrderSimpleVO intentionModel;

    /**
     * 拍摄模特id
     */
    @ApiModelProperty(value = "拍摄模特id")
    @JsonIgnore
    private Long shootModelId;

    /**
     * 拍摄模特
     */
    @ApiModelProperty(value = "拍摄模特")
    private ModelOrderSimpleVO shootModel;

    /**
     * 匹配单
     */
    @ApiModelProperty(value = "匹配单")
    private VideoMatchOrderVO videoMatchOrderVO;

    /**
     * 标记物流状态（1:标记发货）
     */
    @ApiModelProperty(value = "标记物流状态（1:标记发货）", notes = "1:标记发货")
    private Integer logisticFlag;
    /**
     * 标记发货原因
     */
    @ApiModelProperty(value = "标记发货原因", notes = "标记发货原因")
    private String logisticFlagRemark;
    /**
     * 标记发货原因
     */
    @ApiModelProperty(value = "标记发货时间", notes = "标记发货时间")
    @JsonFormat(pattern = DatePattern.NORM_DATETIME_PATTERN)
    private Date logisticFlagTime;
    /**
     * 是否吐槽
     */
    @ApiModelProperty(value = "是否吐槽", notes = "是否吐槽")
    private Boolean isRoast;
    /**
     * 发货备注
     */
    @ApiModelProperty(value = "发货备注")
    private String shippingRemark;

    /**
     * 发货图片(关联资源id)
     */
    @ApiModelProperty(value = "发货图片(关联资源id)")
    @JsonIgnore
    private String shippingPic;

    /**
     * 发货图片
     */
    @ApiModelProperty(value = "发货图片")
    private List<String> shippingPics = new ArrayList<>();

    /**
     * 对接人id
     */
    @ApiModelProperty(value = "对接人id")
    @JsonIgnore
    private Long contactId;

    /**
     * 对接人
     */
    @ApiModelProperty(value = "对接人")
    private UserVO contact;

    /**
     * 出单人id
     */
    @ApiModelProperty(value = "出单人id")
    @JsonIgnore
    private Long issueId;

    /**
     * 出单人
     */
    @ApiModelProperty(value = "出单人")
    private UserVO issue;

    /**
     * 视频金额（单位：￥）
     */
    @ApiModelProperty(value = "视频金额（单位：￥）")
    private BigDecimal amount;

    @ApiModelProperty(value = "视频活动优惠金额")
    private BigDecimal videoPromotionAmount;

    /**
     * 视频金额（单位：$）
     */
    @ApiModelProperty(value = "视频金额（单位：$）")
    private BigDecimal amountDollar;

    @ApiModelProperty(value = "需支付金额（单位：￥）")
    @Excel(name = "需支付金额（单位：￥）")
    private BigDecimal payAmount;

    @ApiModelProperty(value = "需支付金额（单位：$）")
    @Excel(name = "需支付金额（单位：$）")
    private BigDecimal payAmountDollar;


    /**
     * 视频价格（单位：$）
     */
    @ApiModelProperty(value = "视频价格（单位：$）")
    private BigDecimal videoPrice;

    /**
     * 图片费用（单位：$）
     */
    @ApiModelProperty(value = "图片费用（单位：$）")
    private BigDecimal picPrice;

    /**
     * 佣金代缴税费（单位：$）
     */
    @ApiModelProperty(value = "佣金代缴税费（单位：$）")
    private BigDecimal commissionPaysTaxes;

    /**
     * 手续费（单位：$）
     */
    @ApiModelProperty(value = "手续费（单位：$）")
    private BigDecimal exchangePrice;

    /**
     * 服务费用（单位：$）
     */
    @ApiModelProperty(value = "服务费用（单位：$）")
    private BigDecimal servicePrice;

    /**
     * 是否有运营反馈素材的链接
     */
    @ApiModelProperty(value = "是否有运营反馈素材的链接")
    private Boolean hasFeedBack;

    /**
     * 上传状态（0:已上传,1:未上传，2：待确认上传，3：取消上传，5：无需上传）
     */
    @ApiModelProperty(value = "上传状态（0:已上传,1:未上传，2：待确认上传，3：取消上传，5：无需上传）")
    private Integer uploadStatus;

    @ApiModelProperty(value = "运营上传的链接")
    private String uploadLink;

    /**
     * 拍摄建议（原拍摄要求）
     */
    @ApiModelProperty("拍摄建议（原拍摄要求）")
    private List<OrderVideoContent> shootRequired;

    /**
     * 产品卖点
     */
    @ApiModelProperty("产品卖点")
    private String sellingPointProduct;

    /**
     * 模特要求（原匹配模特注意事项）
     */
    @ApiModelProperty("模特要求（原匹配模特注意事项）")
    private OrderVideoCautionsVO orderVideoCautionsVO;

    /**
     * 剪辑要求
     */
    @ApiModelProperty(value = "剪辑要求")
    private List<OrderVideoContent> clipsRequired;

    @ApiModelProperty(value = "商品规格要求")
    private String orderSpecificationRequire;

    @ApiModelProperty(value = "特别强调")
    private String particularEmphasis;

    @ApiModelProperty(value = "特别强调图片")
    @JsonIgnore
    private String particularEmphasisPicIds;

    @ApiModelProperty(value = "特别强调图片")
    private List<String> particularEmphasisPic;

    /**
     * 是否有未回复的反馈
     */
    @ApiModelProperty("是否有未回复的反馈")
    private Boolean hasCase;

    /**
     * 模特佣金单位（美金:USD,加币:CAD,英镑:GBP,欧元:EUR）
     */
    @ApiModelProperty(value = "模特佣金单位（美金:USD,加币:CAD,英镑:GBP,欧元:EUR）")
    private String commissionUnit;

    /**
     * 模特佣金
     */
    @ApiModelProperty(value = "模特佣金")
    private BigDecimal commission;

    /**
     * 超额说明
     */
    @ApiModelProperty(value = "超额说明")
    private String overstatement;

    /**
     * 订单标记（1:已被标记）
     */
    @ApiModelProperty(value = "订单标记（1:已被标记）")
    private Integer orderFlag;

    /**
     * 订单退款信息
     */
    @ApiModelProperty(value = "订单退款信息")
    private OrderVideoRefundSimpleVO orderVideoRefund;


    @ApiModelProperty(value = "订单退款信息(用于前端判断是否补偿订单或照片退款）")
    private List<OrderVideoRefundSimpleVO> orderVideoRefundList;

    /**
     * 订单进入待确认的时间
     */
    @ApiModelProperty(value = "订单进入待确认的时间")
    @JsonFormat(pattern = DatePattern.NORM_DATETIME_PATTERN)
    private Date unConfirmTime;

    /**
     * 订单进入待完成的时间
     */
    @ApiModelProperty(value = "订单进入待完成的时间")
    @JsonFormat(pattern = DatePattern.NORM_DATETIME_PATTERN)
    private Date unFinishedTime;

    /**
     * 视频订单自动完成时间
     */
    @ApiModelProperty(value = "视频订单自动完成时间")
    @JsonFormat(pattern = DatePattern.NORM_DATETIME_PATTERN)
    private Date autoCompleteTime;

    /**
     * 视频订单最后变更时间
     */
    @ApiModelProperty(value = "视频订单最后变更时间")
    @JsonFormat(pattern = DatePattern.NORM_DATETIME_PATTERN)
    private Date lastChangeTime;

    /**
     * 创建订单商家id
     */
    @ApiModelProperty(value = "创建订单商家id")
    private Long createOrderBusinessId;

    /**
     * 创建订单用户id
     */
    @ApiModelProperty(value = "创建订单用户id")
    private Long createOrderUserId;

    @ApiModelProperty(value = "创建订单登录用户id")
    private Long createOrderBizUserId;

    /**
     * 创建订单用户账号
     */
    @ApiModelProperty(value = "创建订单用户账号")
    private String createOrderUserAccount;

    /**
     * 创建订单用户名称（订单运营）
     */
    @ApiModelProperty(value = "创建订单用户名称（订单运营）")
    private String createOrderUserName;

    /**
     * 创建订单用户微信名称（订单运营）
     */
    @ApiModelProperty(value = "创建订单用户微信名称（订单运营）")
    private String createOrderUserNickName;

    /**
     * 物流信息
     */
    @ApiModelProperty(value = "物流信息")
    private OrderLogisticInfoVO logisticInfo;

    @ApiModelProperty(value = "物流跟进数据")
    private LogisticFollowVideoInfoVO logisticFollowVideoInfoVO;

    /**
     * 发货时间（商家/运营 点击发货的时间）
     */
    @ApiModelProperty(value = "发货时间（商家/运营 点击发货的时间）")
    @JsonFormat(pattern = DatePattern.NORM_DATETIME_PATTERN)
    private Date shippingTime;

    /**
     * 视频订单催单次数
     */
    @ApiModelProperty(value = "视频订单催单次数")
    private Integer reminder;

    /**
     * 携带类型（1:主携带,2:被携带）
     */
    @ApiModelProperty(value = "携带类型（1:主携带,2:被携带）")
    private Integer carryType;

    /**
     * 是否有备注（true：有）
     */
    @ApiModelProperty(value = "是否有备注（true：有）")
    private Boolean hasComment;

    /**
     * 是否有模特反馈的素材（true：有）
     */
    @ApiModelProperty(value = "是否有模特反馈的素材（true：有）")
    private Boolean hasMaterial;

    /**
     * 是否有历史变更记录（true：有）
     */
    @ApiModelProperty(value = "是否有历史变更记录（true：有）")
    private Boolean hasChangeLog;

    /**
     * 注意事项图片（FK:order_resource.id，多个用,隔开）
     */
    @ApiModelProperty(value = "注意事项图片（FK:order_resource.id，多个用,隔开）")
    @JsonIgnore
    private String cautionsPicId;

    /**
     * 售后单状态
     */
    @ApiModelProperty(value = "售后单状态（1：待处理，4：已完成）")
    private Integer afterSaleTaskStatus;

    /**
     * 工单状态
     */
    @ApiModelProperty(value = "工单状态（1：待处理，4：已完成）")
    private Integer workOrderTaskStatus;

    @ApiModelProperty(value = "当前汇率")
    @Excel(name = "当前汇率")
    private BigDecimal currentExchangeRate;

    /**
     * 回退ID (FK:order_video_rollback_record.id)
     */
    @ApiModelProperty("回退ID")
    private Long rollbackId;

    /**
     * 是否有未完成的剪辑任务
     */
    @ApiModelProperty("是否有未完成的剪辑任务")
    private Boolean hasUnfinishedEditTask;

    /**
     * 视频订单最新的反馈给商家素材
     */
    @ApiModelProperty("视频订单最新的反馈给商家素材")
    private OrderVideoFeedBack latestFeedBack;

    /**
     * 订单参与的活动以及对应的优惠
     */
    @ApiModelProperty(value = "订单参与的活动以及对应的优惠")
    private List<OrderDiscountDetailVO> orderDiscountDetailVOS;

    /**
     * 是通品（1：是，0：不是）
     */
    @ApiModelProperty(value = "是通品（1：是，0：不是）")
    private Integer isGund;

    /**
     * 是否有新的反馈给商家素材
     */
    @ApiModelProperty("是否有新的反馈给商家素材")
    private Boolean hasNewFeedBack;

    /**
     * 是否有被商家驳回的模特
     */
    @ApiModelProperty("是否有被商家驳回的模特")
    private Boolean hasRejectedModel;
}
