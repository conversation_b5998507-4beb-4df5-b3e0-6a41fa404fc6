package com.ruoyi.system.api.domain.vo.biz.model;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * 模特姓名和账户名组合VO
 * 用于下拉筛选查询结果封装
 *
 * <AUTHOR>
 * @date 2024-07-25
 */
@Data
public class ModelNameAccountVO {
    
    /**
     * 模特姓名+账户名组合（格式：姓名+账户名）
     */
    @ApiModelProperty("模特姓名+账户名组合（格式：姓名+账户名）")
    private String nameAccount;
    
    /**
     * 创建时间（用于排序）
     */
    @ApiModelProperty("创建时间（用于排序）")
    private Date createTime;

    /**
     * 模特ID
     */
    @ApiModelProperty("模特ID")
    private Long id;
}
