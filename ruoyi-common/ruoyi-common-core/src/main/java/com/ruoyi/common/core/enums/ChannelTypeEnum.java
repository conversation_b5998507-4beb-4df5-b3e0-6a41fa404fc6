package com.ruoyi.common.core.enums;


import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

/**
 * 渠道类型枚举
 *
 * <AUTHOR>
 * @date 2024/9/25
 */
@Getter
@AllArgsConstructor
@NoArgsConstructor
public enum ChannelTypeEnum {

    NORMAL(0,"Normal", "PT", "普通渠道"),
    MARKETING(1,"Marketing", "SC", "营销市场渠道"),
    DISTRIBUTION(2, "Distribution", "FX","分销渠道"),
    BUSINESS(3, "Business", "BS","子账号"),
    WEBSITE(4, "Website", "SYS","系统官网"),
    SEO(5, "Seo", "SEO","系统SEO"),
    VIP(6, "Vip", "VIP","VIP页"),
    FISSION(7, "Fission", "LB","裂变"),
    WX_AD(8, "wxad_button", "WX","微信广告")
    ;

//    系统官网
    public static final String WEBSITE_TAG = "etPyXlDQAAIgWpJWzoXZRHSSem44ywfw";
//    系统子账号
    public static final String BUSINESS_TAG = "etPyXlDQAAj521XCftufxdSZjRSzKHTw";
//    系统SEO
    public static final String SEO_TAG = "etPyXlDQAAcMPgygiVOjQv75NM7TKEvA";

//    系统VIP介绍页面
    public static final String VIP_PAGE_TAG = "etPyXlDQAAnXt0KKZrdaK_UUYqs_3NBw";

//    系统微信投流
    public static final String WX_AD_BUTTON = "etPyXlDQAAGMx7X_MmFQ4qsI0KrESzMw";


    public static final String PREFIX = "Sys-";

    private Integer code;
    private String label;
    private String tagLabel;
    private String desc;

    public static ChannelTypeEnum findByCode(Integer code) {
        for (ChannelTypeEnum value : ChannelTypeEnum.values()) {
            if (value.code.equals(code)) {
                return value;
            }
        }
        return null;
    }

    public static String getDescByCode(int code) {
        for (ChannelTypeEnum channel : ChannelTypeEnum.values()) {
            if (channel.code == code) {
                return channel.desc;
            }
        }
        return "Unknown Code";
    }
}
