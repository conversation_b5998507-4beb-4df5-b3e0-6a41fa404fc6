package com.wnkx.biz.model.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.common.core.domain.vo.UserVO;
import com.ruoyi.system.api.domain.dto.ModelListDTO;
import com.ruoyi.system.api.domain.dto.biz.business.BusinessAccountCollectModelDTO;
import com.ruoyi.system.api.domain.dto.biz.model.*;
import com.ruoyi.system.api.domain.entity.Model;
import com.ruoyi.system.api.domain.entity.ModelPerson;
import com.ruoyi.system.api.domain.entity.order.datastatistics.CustomerServiceAddedOustModelCountInfo;
import com.ruoyi.system.api.domain.vo.biz.business.user.ModelBlackListUserVO;
import com.ruoyi.system.api.domain.vo.biz.model.*;
import com.ruoyi.system.api.domain.vo.order.datastatistics.EnglishCustomerServiceDataVO;
import org.springframework.validation.annotation.Validated;

import javax.validation.constraints.NotBlank;
import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * 模特信息Service接口
 * 
 * <AUTHOR>
 * @date 2024-05-20
 */
@Validated
public interface IModelService extends IService<Model>
{
    /**
     * 查询模特信息
     * 
     * @param id 模特信息主键
     * @return 模特信息
     */
    ModelVO selectModelById(Long id);

    /**
     * 查询模特信息列表
     *
     * @param modelListDTO 模特信息
     * @return 模特信息集合
     */
    List<ModelVO> selectModelListByCondition(ModelListDTO modelListDTO);

    /**
     * 查询模特家庭信息列表
     * @param modelListDTO
     * @return
     */
    List<ModelVO> selectModelFamilyListByCondition(ModelListDTO modelListDTO);

    /**
     * 根据家庭ID获取模特家庭模卡信息
     * @param familyId
     * @return
     */
    List<ModelFamilyVO> selectModelFamilyByFamilyId(Long familyId);

    /**
     * 添加模特家庭成员
     * @param dto
     */
    void addFamilyModel(ModelFamilyInsertDTO dto);

    /**
     * 删除模特家庭成员
     * @param dto
     */
    void deleteFamilyModel(ModelFamilyDeleteDTO dto);

    /**
     * 新增模特信息
     * 
     * @param modelDTO 模特信息
     */
    void insertModel(ModelDTO modelDTO);

    /**
     * 修改模特信息
     * 
     * @param modelDTO 模特信息
     */
    void updateModel(ModelDTO modelDTO);

    /**
     * 删除模特关联表
     */
    void removeModelRelevance(Long id);

    /**
     * 添加模特关联表
     */
    void saveModelRelevance(ModelDTO modelDTO, Long modelId);
    /**
     * 更新模特状态
     */
    void updateModelStatus(ModelUpdateStatusDTO dto);

    /**
     * 置顶模特
     */
    void top(Long id);

    /**
     * 获取模特家庭成员数量
     * @return
     */
    Map<Long, Integer> getFamilyCountMap();
    /**
     * 模特关联人员
     */
    List<UserVO> relevance(Long id);

    /**
     * 修改模特关联人员
     */
    void updateRelevance(ModelRelevanceDTO modelRelevanceDTO);

    /**
     * 创建模特后台链接
     */
    String backgroundLink(Long id);

    /**
     * 查询模特信息列表（内部请求）
     */
    List<ModelInfoVO> innerList(ModelListDTO modelListDTO);

    /**
     * 查询不可接单的模特（内部请求）
     */
    List<Model> queryCannotAcceptList(CannotAcceptModelDTO dto);

    /**
     * 模特列表下拉框
     */
    List<ModelVO> selectList(ModelListDTO modelListDTO);

    /**
     * 查询模特关联运营
     */
    List<ModelPerson> queryModelPerson(Collection<Long> modelIds);

    /**
     * 添加预选模特列表
     */
    PreselectModelListResultVO addPreselectModelList(AddPreselectModelListDTO addPreselectModelListDTO);

    /**
     * 组装导出信息
     */
    List<ModelExportDTO> getExportModel(List<ModelVO> list);

    /**
     * 根据模特账号Id获取模特数据
     * @param account
     * @return
     */
    ModelVO selectModelByModelAccount(@NotBlank(message = "账号不能为空") String account);

    /**
     * 更新最新登录时间
     * @param account
     */
    void loginForAccount(@NotBlank(message = "账号不能为空") String account);

    /**
     * 模特列表-获取关联人员下拉框（运营端）
     */
    List<UserVO> modelPersonsSelect(String keyword);

    /**
     * 模糊查询模特信息列表（模特名称、模特账号）
     */
    List<ModelInfoVO> queryLikeModelList(ModelListDTO modelListDTO);

    /**
     * 模特行程时间开始与结束 更新模特状态
     */
    void updateModelTravelStatus();

    /**
     * 运营端-编辑订单-更换模特列表
     */
    List<ModelVO> editOrderChangeModelList(EditOrderChangeModelListDTO dto);

    /**
     * 查询模特简单信息（用于订单列表模特数据）
     */
    List<ModelOrderSimpleVO> queryModelSimpleList(ModelListDTO modelListDTO);

    /**
     * 查询模特信息变更记录
     */
    List<ModelChangeRecordVO> modelChangeRecord(Long modelId);

    /**
     * 查询模特信息列表（无需登录）
     */
    List<ModelListSimpleVO> referenceList(BusinessAccountCollectModelDTO dto);

    List<ModelChangeVO> selectModelChangeList(ModelListDTO dto);

    /**
     * 置顶模特数量统计
     */
    ModelTopCountVO topCount();

    /**
     * 修改排序
     */
    void updateSort(ModelSortDTO modelSortDTO);

    /**
     * 查询模特信息（预选模特对象）
     */
    List<AddPreselectModelListVO> selectModelInfoOfPreselection(ModelListDTO modelListDTO);

    String getMsg(String modelId);
    /**
     * 获取模特被拉黑登录账号集合
     * @param modelId
     * @return
     */
    List<ModelBlackListUserVO> queryModelBlackListUserVO(Long modelId);

    ModelVO selectModelByModelLoginAccount(String account);

    /**
     * 通过模特状态查询模特
     */
    List<Model> selectListByStatus(Integer status);

    /**
     * 获取英文部客服 新增/淘汰模特数
     */
    List<CustomerServiceAddedOustModelCountInfo> getEnglishCustomerServiceAddedOustModelCounts(String date);

    /**
     * 客服数据-获取英文部关联模特数据
     */
    List<EnglishCustomerServiceDataVO> selectEnglishCustomerServiceModelData();

    /**
     * 查询模特姓名和账户名组合列表（用于下拉筛选）
     *
     * @return 模特姓名+账户名的字符串列表
     */
    List<ModelNameAccountVO> selectModelNameAccountOptions();
}
