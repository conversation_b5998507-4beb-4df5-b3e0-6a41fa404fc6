package com.wnkx.biz.channel.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.system.api.domain.dto.biz.business.account.user.BizUserChannelDTO;
import com.ruoyi.system.api.domain.dto.biz.business.account.user.BizUserChannelStatisticsDTO;
import com.ruoyi.system.api.domain.dto.biz.business.account.user.BizUserDetailListDTO;
import com.ruoyi.system.api.domain.dto.biz.channel.InviteListDTO;
import com.ruoyi.system.api.domain.dto.biz.channel.UserChannelStatisticsDTO;
import com.ruoyi.system.api.domain.entity.biz.channel.BizUserChannel;
import com.ruoyi.system.api.domain.vo.biz.business.user.BizUserDetailVO;
import com.ruoyi.system.api.domain.vo.biz.business.user.BizUserListVO;
import com.ruoyi.system.api.domain.vo.biz.channel.ChannelInviteVO;
import com.ruoyi.system.api.domain.vo.biz.channel.UserChannelStatisticsVO;
import org.springframework.validation.annotation.Validated;

import javax.validation.Valid;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @description 针对表【biz_user_channel(用户渠道信息表)】的数据库操作Service
 * @createDate 2024-09-24 17:02:16
 */
@Validated
public interface IBizUserChannelService extends IService<BizUserChannel> {

    /**
     * 初始化用户渠道信息
     *
     * @param dto
     * @return
     */
    BizUserChannel initBizUserChannel(@Valid BizUserChannelDTO dto);

    /**
     * 修改用户渠道信息
     *
     * @param dto
     */
    void updateBizUserChannel(@Valid BizUserChannelDTO dto);

    /**
     * 获取渠道类型统计
     *
     * @param dto
     * @return
     */
    Map<Long, UserChannelStatisticsVO> getUserChannelStatisticsVO(@Valid UserChannelStatisticsDTO dto);


    /***
     * 根据渠道类型获取统计数量
     * @param dto
     * @return
     */
    UserChannelStatisticsVO getTotalStatisticsVO(BizUserChannelStatisticsDTO dto);

    /**
     * 获取邀请渠道列表
     *
     * @param dto
     * @return
     */
    List<ChannelInviteVO> inviteList(InviteListDTO dto);

    /**
     * 激活登录账号
     *
     * @param bizUserId
     */
    void activate(Long bizUserId);

    /**
     * 获取用户渠道
     * @param dto
     * @return
     */
    List<BizUserDetailVO> getUserChannel(BizUserDetailListDTO dto);

    /**
     * 获取邀请注册用户列表（包含用户详细信息）
     * @param distributionChannelId 分销渠道ID
     * @return 用户列表
     */
    List<BizUserListVO> inviteRegisterListWithUserInfo(Long distributionChannelId);
}
