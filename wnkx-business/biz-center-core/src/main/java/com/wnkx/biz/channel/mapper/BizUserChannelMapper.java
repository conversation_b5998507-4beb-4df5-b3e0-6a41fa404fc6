package com.wnkx.biz.channel.mapper;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.ruoyi.common.core.enums.ChannelTypeEnum;
import com.ruoyi.common.core.enums.StatusTypeEnum;
import com.ruoyi.system.api.domain.dto.biz.business.account.user.BizUserChannelDTO;
import com.ruoyi.system.api.domain.dto.biz.business.account.user.BizUserChannelStatisticsDTO;
import com.ruoyi.system.api.domain.dto.biz.business.account.user.BizUserDetailListDTO;
import com.ruoyi.system.api.domain.dto.biz.channel.InviteListDTO;
import com.ruoyi.system.api.domain.dto.biz.channel.UserChannelStatisticsDTO;
import com.ruoyi.system.api.domain.entity.biz.channel.BizUserChannel;
import com.ruoyi.system.api.domain.vo.biz.business.user.BizUserDetailVO;
import com.ruoyi.system.api.domain.vo.biz.business.user.BizUserListVO;
import com.ruoyi.system.api.domain.vo.biz.channel.ChannelInviteVO;
import com.ruoyi.system.api.domain.vo.biz.channel.UserChannelStatisticsVO;
import com.wnkx.db.mapper.SuperMapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @description 针对表【biz_user_channel(用户渠道信息表)】的数据库操作Mapper
 * @createDate 2024-09-24 17:02:16
 * @Entity com.ruoyi.system.api.domain.entity.biz.channel.BizUserChannel
 */
public interface BizUserChannelMapper extends SuperMapper<BizUserChannel> {

    /**
     * 根据登录账号id修改用户渠道信息
     *
     * @param dto
     */
    default void updateByBizUserId(BizUserChannelDTO dto) {
        this.update(null, new LambdaUpdateWrapper<BizUserChannel>()
                .set(ObjectUtil.isNotNull(dto.getRegisterChannelId()), BizUserChannel::getRegisterChannelId, dto.getRegisterChannelId())
                .set(ObjectUtil.isNotNull(dto.getRegisterChannelType()), BizUserChannel::getRegisterChannelType, dto.getRegisterChannelType())
                .set(ObjectUtil.isNotNull(dto.getRegisterTime()), BizUserChannel::getRegisterTime, dto.getRegisterTime())
                .set(ObjectUtil.isNotNull(dto.getWechatChannelId()), BizUserChannel::getWechatChannelId, dto.getWechatChannelId())
                .set(ObjectUtil.isNotNull(dto.getWechatChannelType()), BizUserChannel::getWechatChannelType, dto.getWechatChannelType())
                .set(ObjectUtil.isNotNull(dto.getAddWechatTime()), BizUserChannel::getAddWechatTime, dto.getAddWechatTime())
                .eq(BizUserChannel::getBizUserId, dto.getBizUserId())
        );
    }

    /***
     * 获取注册渠道统计数据
     * @param dto
     * @return
     */
    List<UserChannelStatisticsVO> getRegisterUserChannelStatistics(UserChannelStatisticsDTO dto);

    /**
     * 获取添加企微统计数量
     *
     * @param dto
     * @return
     */
    List<UserChannelStatisticsVO> getWeChatUserChannelStatistics(UserChannelStatisticsDTO dto);

    /**
     * 获取渠道统计数据
     *
     * @param dto
     * @return
     */
    UserChannelStatisticsVO getTotalStatisticsVO(BizUserChannelStatisticsDTO dto);

    /**
     * 获取邀请渠道列表*
     *
     * @param dto
     * @return
     */
    List<ChannelInviteVO> inviteList(InviteListDTO dto);

    /**
     * 激活登录账号
     * @param bizUserId
     */
    default void activate(Long bizUserId) {
        this.update(null, new LambdaUpdateWrapper<BizUserChannel>()
                .set(BizUserChannel::getIsActivate, StatusTypeEnum.YES.getCode())
                .eq(BizUserChannel::getBizUserId, bizUserId));

    }

    List<BizUserDetailVO> selectUserChannelByUserId(@Param("dto") BizUserDetailListDTO dto);

    default BizUserChannel getUserChannelOne(Long bizUserId){
        return this.selectOne(Wrappers.lambdaQuery(BizUserChannel.class).eq(BizUserChannel::getBizUserId, bizUserId));
    }

    /**
     * 获取邀请注册用户列表（包含用户详细信息）
     * @param distributionChannelId 分销渠道ID
     * @return 用户列表
     */
    List<BizUserListVO> inviteRegisterListWithUserInfo(Long distributionChannelId);
}




