package com.wnkx.biz.core;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.ruoyi.common.core.constant.CacheConstants;
import com.ruoyi.common.core.constant.ConfigConstants;
import com.ruoyi.common.core.constant.Constants;
import com.ruoyi.common.core.constant.TokenConstants;
import com.ruoyi.common.core.enums.ChannelTypeEnum;
import com.ruoyi.common.core.exception.ServiceException;
import com.ruoyi.common.core.text.Convert;
import com.ruoyi.common.core.utils.DateUtils;
import com.ruoyi.common.core.utils.RandomCodeUtil;
import com.ruoyi.common.core.utils.StringUtils;
import com.ruoyi.common.redis.service.RedisService;
import com.ruoyi.common.security.utils.SecurityUtils;
import com.ruoyi.system.api.domain.dto.biz.business.account.user.BizUserChannelDTO;
import com.ruoyi.system.api.domain.entity.biz.channel.DistributionChannel;
import com.ruoyi.system.api.domain.entity.biz.channel.MarketingChannel;
import com.ruoyi.system.api.domain.entity.biz.wechat.WeChatExternalUser;
import com.ruoyi.system.api.domain.entity.biz.wechat.WeChatTag;
import com.ruoyi.system.api.domain.vo.biz.channel.ChannelVO;
import com.ruoyi.system.api.domain.vo.biz.channel.DistributionChannelVO;
import com.ruoyi.system.api.domain.vo.biz.channel.ChannelBrokeRageVO;
import com.wnkx.biz.channel.mapper.DistributionChannelMapper;
import com.wnkx.biz.channel.mapper.MarketingChannelMapper;
import com.wnkx.biz.channel.service.IBizUserChannelService;
import com.wnkx.biz.remote.RemoteService;
import com.wnkx.biz.wechat.mapper.WeChatTagMapper;
import com.wnkx.biz.wechat.service.WorkWechatApiService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @program :woniu-world
 * @description :
 * @create :2024-09-27 14:37
 **/
@Component
@RequiredArgsConstructor
@Slf4j
public class ChannelCore {
    private final MarketingChannelMapper marketingChannelMapper;
    private final DistributionChannelMapper distributionChannelMapper;
    private final IBizUserChannelService bizUserChannelService;
    private final WeChatTagMapper weChatTagMapper;

    private final WorkWechatApiService workWechatApiService;
    private static final Long reentrancyCount = 5L;
    private static final Long expireTime = 10L;
    private final RedisService redisService;
    private final RemoteService remoteService;

    @Transactional(rollbackFor = Exception.class)
    public void editTag(ChannelTypeEnum channelType, String tagId, String tagName) {
        workWechatApiService.editTag(channelType, tagId, tagName);
        weChatTagMapper.updateTagNameByTagId(tagId, channelType.getTagLabel() + "-" + tagName);

    }

    public ChannelBrokeRageVO getFissionDiscountV1() {
        String key = Constants.SYS_CONFIG_KEY + ConfigConstants.SYS_CHANNEL_FISSION_MEMBER_DISCOUNT_V1;
        String result = redisService.getCacheObject(key);
        if (StrUtil.isNotBlank(result)) {
            return JSON.parseObject(result, ChannelBrokeRageVO.class);
        }
        String configKey = remoteService.getConfigKey(key);
        return JSON.parseObject(configKey, ChannelBrokeRageVO.class);
    }

    public ChannelBrokeRageVO getChannelMemberDiscountV1() {
        String key = Constants.SYS_CONFIG_KEY + ConfigConstants.SYS_CHANNEL_MEMBER_DISCOUNT_V1;
        String result = redisService.getCacheObject(key);
        if (StrUtil.isNotBlank(result)) {
            return JSON.parseObject(result, ChannelBrokeRageVO.class);
        }
        String configKey = remoteService.getConfigKey(key);
        return JSON.parseObject(configKey, ChannelBrokeRageVO.class);
    }

    public DistributionChannel saveDistributionChannel(String channelName, DistributionChannel distributionChannel, ChannelTypeEnum channelTypeEnum) {
        String redisKey = RandomCodeUtil.generateRandomCode(8);
        String lingCode = initLinkCode(redisKey, channelTypeEnum);
        String seedCode = initSeedCode(redisKey);
        String seedId = initSeedId(redisKey);
        distributionChannel.setCreateBy(SecurityUtils.getUsername());
        distributionChannel.setCreateId(SecurityUtils.getUserId());
        distributionChannel.setDedicatedLinkCode(lingCode);
        distributionChannel.setDisableTime(DateUtils.getNowDate());
        distributionChannel.setWeChatUrl(workWechatApiService.contactMeQrcode(channelTypeEnum, lingCode));
        distributionChannel.setSeedCode(seedCode);
        distributionChannel.setSeedId(seedId);
        distributionChannel.setTagId(workWechatApiService.addTag(channelTypeEnum, getWeChatTag(channelName, seedCode)));
        if (ObjectUtil.isNull(distributionChannel.getBizUserId())) {
            distributionChannel.setBizUserId(0L);
        }
        distributionChannel.setPassword(RandomCodeUtil.generateRandomCode(RandomCodeUtil.ALL_NUMBER, 4));
        distributionChannelMapper.insert(distributionChannel);
        return distributionChannel;
    }

//    public BigDecimal getFissionBrokeRage(){
//        return distributionChannelMapper.getFissionBrokeRage();
//    }

    public DistributionChannel getFissionOne() {
        DistributionChannel fissionOne = distributionChannelMapper.getFissionOne();
        return fissionOne;
    }


    /**
     * 获取企业微信标签
     *
     * @param channelName 渠道名称
     * @param seedCode    种草码
     * @return
     */
    private String getWeChatTag(String channelName, String seedCode) {
        Assert.notNull(channelName, "渠道名称不能为空");
        Assert.notNull(seedCode, "种草码不能为空");
        return channelName + "-" + seedCode;
    }

    /**
     * 生成专属链接code
     *
     * @param redisKey
     * @return
     */
    public String initLinkCode(String redisKey, ChannelTypeEnum channelTypeEnum) {
        String key = CacheConstants.DISTRIBUTION_CHANNEL_LINK_CODE + redisKey;
        if (redisService.setIncr(key, expireTime, TimeUnit.SECONDS).compareTo(reentrancyCount) > 0) {
            throw new ServiceException("系统生成随机数失败，请稍后重试！");
        }
        String linkCode = channelTypeEnum.getTagLabel() + RandomCodeUtil.generateRandomCode(8);

        DistributionChannel distributionChannel = distributionChannelMapper.getByLinkCode(linkCode);
        if (ObjectUtil.isNotNull(distributionChannel)) {
            return initLinkCode(redisKey, channelTypeEnum);
        }
        return linkCode;
    }

    /**
     * 生成种草码
     *
     * @param redisKey
     * @return
     */
    public String initSeedCode(String redisKey) {
        String key = CacheConstants.DISTRIBUTION_CHANNEL_SEED_CODE + redisKey;
        if (redisService.setIncr(key, expireTime, TimeUnit.SECONDS).compareTo(reentrancyCount) > 0) {
            throw new ServiceException("系统生成随机数失败，请稍后重试！");
        }
        String seedCode = RandomCodeUtil.generateRandomCode(RandomCodeUtil.SIMPLE_NUMBER + RandomCodeUtil.CAPITAL_LETTER, 3);
        if (StrUtil.isNumeric(seedCode)) {
            //全数字 填充一位英文
            seedCode = seedCode + RandomCodeUtil.generateRandomCode(RandomCodeUtil.CAPITAL_LETTER, 1);
        } else if (seedCode.matches("^[A-Z]+$")) {
            //全英文 填充一位数字
            seedCode = seedCode + RandomCodeUtil.generateRandomCode(RandomCodeUtil.SIMPLE_NUMBER, 1);
        } else {
            //随机填充一位
            seedCode = seedCode + RandomCodeUtil.generateRandomCode(RandomCodeUtil.SIMPLE_NUMBER + RandomCodeUtil.CAPITAL_LETTER, 1);
        }
        DistributionChannel distributionChannel = distributionChannelMapper.getBySeedCode(seedCode);
        if (ObjectUtil.isNotNull(distributionChannel)) {
            return initSeedCode(redisKey);
        }
        return seedCode;
    }

    /**
     * 生成种草码
     *
     * @param redisKey
     * @return
     */
    public String initSeedId(String redisKey) {
        String key = CacheConstants.DISTRIBUTION_CHANNEL_SEED_ID + redisKey;
        if (redisService.setIncr(key, expireTime, TimeUnit.SECONDS).compareTo(reentrancyCount) > 0) {
            throw new ServiceException("系统生成随机数失败，请稍后重试！");
        }
        String seedId = RandomCodeUtil.generateRandomCode(RandomCodeUtil.ALL_NUMBER, 6);
        DistributionChannel distributionChannel = distributionChannelMapper.getBySeedId(seedId);
        if (ObjectUtil.isNotNull(distributionChannel)) {
            return initSeedId(redisKey);
        }
        return seedId;
    }


    public ChannelVO getChannelByTicket(String ticket) {
        ChannelVO channelVO = new ChannelVO();
        if (StringUtils.isNotBlank(ticket) && ticket.startsWith(TokenConstants.REG)) {
            int ticketOriginNum = TokenConstants.REG.length() + 6;
            getChannel(ticket, channelVO, ticketOriginNum);
        } else if (StringUtils.isNotBlank(ticket) && ticket.startsWith(TokenConstants.REG_PLUS)) {
            int ticketOriginNum = TokenConstants.REG_PLUS.length() + 6;
            getChannel(ticket, channelVO, ticketOriginNum);
        }
        return channelVO;
    }

    private void getChannel(String ticket, ChannelVO channelVO, int ticketOriginNum) {
        if (ticket.length() > ticketOriginNum) {
            String linkCode = ticket.substring(ticketOriginNum);
            if (linkCode.startsWith(ChannelTypeEnum.DISTRIBUTION.getTagLabel())) {
                DistributionChannel distributionChannel = distributionChannelMapper.getByLinkCode(linkCode);
                if (ObjectUtil.isNotNull(distributionChannel)) {
                    BeanUtil.copyProperties(distributionChannel, channelVO);
                    channelVO.setChannelType(ChannelTypeEnum.DISTRIBUTION);
                }
            } else if (linkCode.startsWith(ChannelTypeEnum.MARKETING.getTagLabel())) {
                MarketingChannel marketingChannel = marketingChannelMapper.getQrcodeByDedicatedLinkCode(linkCode);
                BeanUtil.copyProperties(marketingChannel, channelVO);
                channelVO.setChannelType(ChannelTypeEnum.MARKETING);
            } else if (linkCode.startsWith(ChannelTypeEnum.FISSION.getTagLabel())) {
                DistributionChannel distributionChannel = distributionChannelMapper.getByLinkCode(linkCode);
                if (ObjectUtil.isNotNull(distributionChannel)) {
                    BeanUtil.copyProperties(distributionChannel, channelVO);
                    channelVO.setChannelType(ChannelTypeEnum.FISSION);
                }
            }
        }
    }

    public ChannelVO getChannelByWeChatState(String state) {
        ChannelVO channelVO = new ChannelVO();
        channelVO.initSimpleChannel();
        if (state.startsWith(ChannelTypeEnum.PREFIX + ChannelTypeEnum.DISTRIBUTION.getLabel())) {
            DistributionChannel distributionChannel = distributionChannelMapper.getByLinkCode(state.substring((ChannelTypeEnum.PREFIX + ChannelTypeEnum.DISTRIBUTION.getLabel()).length() + 1));
            if (ObjectUtil.isNotNull(distributionChannel)) {
                BeanUtil.copyProperties(distributionChannel, channelVO);
                channelVO.setChannelName(distributionChannel.getChannelName());
                channelVO.setChannelType(ChannelTypeEnum.DISTRIBUTION);
            }
        }
        if (state.startsWith(ChannelTypeEnum.PREFIX + ChannelTypeEnum.MARKETING.getLabel())) {
            MarketingChannel marketingChannel = marketingChannelMapper.getQrcodeByDedicatedLinkCode(state.substring((ChannelTypeEnum.PREFIX + ChannelTypeEnum.MARKETING.getLabel()).length() + 1));
            if (ObjectUtil.isNotNull(marketingChannel)) {
                BeanUtil.copyProperties(marketingChannel, channelVO);
                channelVO.setChannelName(marketingChannel.getMarketingChannelName());
                channelVO.setChannelType(ChannelTypeEnum.MARKETING);
            }
        }
        if (state.startsWith(ChannelTypeEnum.PREFIX + ChannelTypeEnum.WEBSITE.getLabel())) {
//            Sys-Website-1
            channelVO.setChannelName(ChannelTypeEnum.WEBSITE.getDesc());
            channelVO.setId(Long.valueOf(state.substring((ChannelTypeEnum.PREFIX + ChannelTypeEnum.WEBSITE.getLabel()).length() + 1)));
            channelVO.setTagId(ChannelTypeEnum.WEBSITE_TAG);
            channelVO.setChannelType(ChannelTypeEnum.WEBSITE);
        }
        if (state.startsWith(ChannelTypeEnum.PREFIX + ChannelTypeEnum.BUSINESS.getLabel())) {
            channelVO.setChannelName(state.substring((ChannelTypeEnum.PREFIX + ChannelTypeEnum.BUSINESS.getLabel()).length() + 1) + "-" + ChannelTypeEnum.BUSINESS.getDesc());
            channelVO.setTagId(ChannelTypeEnum.BUSINESS_TAG);
            channelVO.setChannelType(ChannelTypeEnum.BUSINESS);
        }
        if (state.startsWith(ChannelTypeEnum.PREFIX + ChannelTypeEnum.FISSION.getLabel())) {
            DistributionChannel distributionChannel = distributionChannelMapper.getByLinkCode(state.substring((ChannelTypeEnum.PREFIX + ChannelTypeEnum.FISSION.getLabel()).length() + 1));
            if (ObjectUtil.isNotNull(distributionChannel)) {
                BeanUtil.copyProperties(distributionChannel, channelVO);
                channelVO.setChannelName(distributionChannel.getChannelName());
                channelVO.setChannelType(ChannelTypeEnum.FISSION);
            }
        }
        if (state.startsWith(ChannelTypeEnum.PREFIX + ChannelTypeEnum.VIP.getLabel())) {
//            Sys-Vip-1
            channelVO.setChannelName(ChannelTypeEnum.VIP.getDesc());
            channelVO.setId(Long.valueOf(state.substring((ChannelTypeEnum.PREFIX + ChannelTypeEnum.VIP.getLabel()).length() + 1)));
            channelVO.setTagId(ChannelTypeEnum.VIP_PAGE_TAG);
            channelVO.setChannelType(ChannelTypeEnum.VIP);
        }
        if (state.startsWith(ChannelTypeEnum.WX_AD.getLabel())) {
//            wxad_button
            channelVO.setChannelName(ChannelTypeEnum.WX_AD.getDesc());
            channelVO.setId(ChannelTypeEnum.WX_AD.getCode().longValue());
            channelVO.setTagId(ChannelTypeEnum.WX_AD_BUTTON);
            channelVO.setChannelType(ChannelTypeEnum.WX_AD);
        }
        return channelVO;
    }

    public void reSetBizUserChannelInfo(WeChatExternalUser weChatExternalUser, Long bizUserId, String ticket) {
        BizUserChannelDTO bizUserChannelDTO = BizUserChannelDTO.builder()
                .bizUserId(bizUserId)
                .addWechatTime(weChatExternalUser.getCreateTime())
//                .registerTime(new Date())
                .build();
        String channel = weChatExternalUser.getChannel();
        //获取 添加微信渠道信息
        if (StrUtil.isNotBlank(channel)) {
            if (channel.startsWith(ChannelTypeEnum.PREFIX + ChannelTypeEnum.DISTRIBUTION.getLabel())) {
                bizUserChannelDTO.setWechatChannelId(Convert.toLong(channel.substring((ChannelTypeEnum.PREFIX + ChannelTypeEnum.DISTRIBUTION.getLabel()).length() + 1)));
                bizUserChannelDTO.setWechatChannelType(ChannelTypeEnum.DISTRIBUTION.getCode());
            } else if (channel.startsWith(ChannelTypeEnum.PREFIX + ChannelTypeEnum.MARKETING.getLabel())) {
                bizUserChannelDTO.setWechatChannelId(Convert.toLong(channel.substring((ChannelTypeEnum.PREFIX + ChannelTypeEnum.MARKETING.getLabel()).length() + 1)));
                bizUserChannelDTO.setWechatChannelType(ChannelTypeEnum.MARKETING.getCode());
            } else if (channel.startsWith(ChannelTypeEnum.PREFIX + ChannelTypeEnum.BUSINESS.getLabel())) {
                bizUserChannelDTO.setWechatChannelType(ChannelTypeEnum.BUSINESS.getCode());
                bizUserChannelDTO.setRegisterChannelType(ChannelTypeEnum.BUSINESS.getCode());
            } else if (channel.startsWith(ChannelTypeEnum.PREFIX + ChannelTypeEnum.WEBSITE.getLabel())) {
                bizUserChannelDTO.setWechatChannelType(ChannelTypeEnum.WEBSITE.getCode());
                bizUserChannelDTO.setRegisterChannelType(ChannelTypeEnum.WEBSITE.getCode());
            } else if (channel.startsWith(ChannelTypeEnum.PREFIX + ChannelTypeEnum.VIP.getLabel())) {
                bizUserChannelDTO.setWechatChannelType(ChannelTypeEnum.VIP.getCode());
                bizUserChannelDTO.setRegisterChannelType(ChannelTypeEnum.VIP.getCode());
            } else if (channel.startsWith(ChannelTypeEnum.PREFIX + ChannelTypeEnum.WX_AD.getLabel())) {
                bizUserChannelDTO.setWechatChannelType(ChannelTypeEnum.WX_AD.getCode());
                bizUserChannelDTO.setRegisterChannelType(ChannelTypeEnum.WX_AD.getCode());
            } else if (channel.startsWith(ChannelTypeEnum.PREFIX + ChannelTypeEnum.FISSION.getLabel())) {
                bizUserChannelDTO.setWechatChannelId(Convert.toLong(channel.substring((ChannelTypeEnum.PREFIX + ChannelTypeEnum.FISSION.getLabel()).length() + 1)));
                bizUserChannelDTO.setWechatChannelType(ChannelTypeEnum.FISSION.getCode());
            }
        }
        ChannelVO ticketChannel = getChannelByTicket(ticket);
        //获取注册用户渠道
        if (ObjectUtil.isNotNull(ticketChannel) && ObjectUtil.isNotNull(ticketChannel.getId())) {
            bizUserChannelDTO.setRegisterChannelId(ticketChannel.getId());
            bizUserChannelDTO.setRegisterChannelType(ticketChannel.getChannelType().getCode());
        }

        bizUserChannelService.updateBizUserChannel(bizUserChannelDTO);
    }

    public List<DistributionChannel> getChannelByIds(List<Long> ids) {
        return distributionChannelMapper.selectBatchIds(ids);
    }

    /**
     * 根据bizUserId获取渠道信息
     *
     * @param bizUserId
     * @return
     */
    public DistributionChannel getChannelByBizUserId(Long bizUserId) {
        return distributionChannelMapper.getChannelByBizUserId(bizUserId);
    }

    public List<MarketingChannel> getMarketingChannelsByIds(List<Long> ids) {
        return marketingChannelMapper.selectBatchIds(ids);
    }

    public DistributionChannelVO getDistributionChannelBySeedCode(String seedCode) {
        return distributionChannelMapper.getDistributionChannelBySeedCode(seedCode);
    }

    public DistributionChannel getDistributionChannelEntityBySeedCode(String seedCode) {
        return distributionChannelMapper.getBySeedCode(seedCode);
    }

    public MarketingChannel getMarketingChannelByBizUserId(Long bizUserId) {
        return marketingChannelMapper.getMarketingChannelByBizUserId(bizUserId);
    }

    /**
     * 检查渠道名称是否可用
     *
     * @param channelTypeEnum 渠道类型
     * @param channelName     分销渠道名称、市场渠道名称
     */
    public void checkChannelName(ChannelTypeEnum channelTypeEnum, String channelName) {
        WeChatTag weChatTag = weChatTagMapper.getByTagName(channelTypeEnum.getTagLabel() + "-" + channelName);
        if (ObjectUtil.isNotNull(weChatTag)) {
            throw new ServiceException("渠道名称已存在，无法使用~");
        }
    }

    /**
     * 获取市场渠道key
     *
     * @param linkCode
     * @return
     */
    public String getVisitKey(String linkCode, String date) {
        return CacheConstants.MARKETING_CHANNEL_VISIT + date + "-" + linkCode;
    }
}
