<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wnkx.biz.channel.mapper.BizUserChannelMapper">

    <resultMap id="BaseResultMap" type="com.ruoyi.system.api.domain.entity.biz.channel.BizUserChannel">
        <id property="id" column="id" jdbcType="BIGINT"/>
        <result property="bizUserId" column="biz_user_id" jdbcType="BIGINT"/>
        <result property="registerChannelType" column="register_channel_type" jdbcType="INTEGER"/>
        <result property="registerChannelId" column="register_channel_id" jdbcType="BIGINT"/>
        <result property="registerTime" column="register_time" jdbcType="TIMESTAMP"/>
        <result property="wechatChannelType" column="wechat_channel_type" jdbcType="INTEGER"/>
        <result property="wechatChannelId" column="wechat_channel_id" jdbcType="BIGINT"/>
        <result property="addWechatTime" column="add_wechat_time" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id
        ,biz_user_id,register_channel_type,
        register_channel_id,register_time,wechat_channel_type,
        wechat_channel_id,add_wechat_time
    </sql>

    <select id="getRegisterUserChannelStatistics"
            resultType="com.ruoyi.system.api.domain.vo.biz.channel.UserChannelStatisticsVO">
        select
        count(*) registerNum,
        register_channel_id channelId
        from biz_user_channel
        <where>
            register_channel_type = #{channelType}
            <if test="channelIds != null and channelIds.size() != 0">
                and register_channel_id in
                <foreach collection="channelIds" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
        </where>
        group by register_channel_id
    </select>

    <select id="getWeChatUserChannelStatistics"
            resultType="com.ruoyi.system.api.domain.vo.biz.channel.UserChannelStatisticsVO">
        select
        count(*) wechatNum,
        sum(is_activate) activateNum,
        wechat_channel_id channelId
        from biz_user_channel
        <where>
            wechat_channel_type = #{channelType}
            <if test="channelIds != null and channelIds.size() != 0">
                and wechat_channel_id in
                <foreach collection="channelIds" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
        </where>
        group by wechat_channel_id
    </select>

    <select id="getTotalStatisticsVO" resultType="com.ruoyi.system.api.domain.vo.biz.channel.UserChannelStatisticsVO">
        select sum(CASE WHEN register_channel_type = #{channelType} THEN 1 ELSE 0 END) registerNum,
               sum(CASE WHEN wechat_channel_type = #{channelType} THEN 1 ELSE 0 END)   wechatNum,
               sum(is_activate)                                                        activateNum
        from biz_user_channel
        <where>
            <if test="registerStartTime != null and registerEndTime != null">
                and register_time BETWEEN #{registerStartTime} AND #{registerEndTime}
            </if>
        </where>
    </select>

    <select id="inviteList" resultType="com.ruoyi.system.api.domain.vo.biz.channel.ChannelInviteVO">
        select * from (select bu.id bizUserId,
        bu.phone,
        bu.nick_name,
        buc.add_wechat_time,
        buc.register_channel_id,
        buc.wechat_channel_id,
        buc.wechat_channel_type,
        buc.register_time,
        msr.member_package_type memberType,
        msr.channel_id memberChannelId,
        buc.register_channel_type
        from biz_user_channel buc
        inner join biz_user bu on buc.biz_user_id = bu.id
        left join member_seed_record msr on bu.id = msr.biz_user_id
        <where>
            wechat_channel_type = #{channelType}
            and wechat_channel_id = #{channelId}
        </where>
        union
        select bu.id bizUserId,
        bu.phone,
        bu.nick_name,
        buc.add_wechat_time,
        buc.register_channel_id,
        buc.wechat_channel_id,
        buc.wechat_channel_type,
        buc.register_time,
        msr.member_package_type memberType,
        msr.channel_id memberChannelId,
        buc.register_channel_type
        from biz_user_channel buc
        inner join biz_user bu on buc.biz_user_id = bu.id
        left join member_seed_record msr on bu.id = msr.biz_user_id
        <where>
            register_channel_type = #{channelType}
            and register_channel_id = #{channelId}
        </where>
        union
        select bu.id bizUserId,
        bu.phone,
        bu.nick_name,
        buc.add_wechat_time,
        buc.register_channel_id,
        buc.wechat_channel_id,
        buc.wechat_channel_type,
        buc.register_time,
        msr.member_package_type memberType,
        msr.channel_id memberChannelId,
        buc.register_channel_type
        from member_seed_record msr
        inner join biz_user bu on bu.id = msr.biz_user_id
        inner join biz_user_channel buc on buc.biz_user_id = bu.id
        <where>
            msr.channel_id = #{channelId}
        </where>
            ) t
        <where>
            <if test="keyword != null and keyword != ''">
                and (t.phone like concat('%', #{keyword}, '%')
                or t.nick_name like concat('%', #{keyword}, '%'))
            </if>
            <if test="memberPackageType != null and memberPackageType != 3 and memberPackageType != 4">
                and t.memberType = #{memberPackageType} and t.memberChannelId = #{channelId}
            </if>
            <if test="memberPackageType != null and memberPackageType == 3">
                and (t.memberChannelId != #{channelId})
            </if>
            <if test="memberPackageType != null and memberPackageType == 4">
                and (t.memberType is null)
            </if>
        </where>

    </select>
    <select id="selectUserChannelByUserId"
            resultType="com.ruoyi.system.api.domain.vo.biz.business.user.BizUserDetailVO">
        SELECT
            buc.biz_user_id AS id,
            buc.register_channel_type,
            buc.wechat_channel_type,
            buc.wechat_channel_id,
            buc.add_wechat_time
        FROM biz_user_channel buc
        <where>
            <if test="dto.bizUserIds != null and dto.bizUserIds.size() > 0">
                buc.biz_user_id IN
                <foreach collection="dto.bizUserIds" item="item" index="index"
                         separator="," open="(" close=")">
                    #{item}
                </foreach>
            </if>
        </where>
    </select>

    <!-- 获取邀请注册用户列表（包含用户详细信息） -->
    <select id="inviteRegisterListWithUserInfo" resultType="com.ruoyi.system.api.domain.vo.biz.business.user.BizUserListVO">
        SELECT
            bu.id,
            bu.name,
            bu.nick_name,
            bu.pic,
            bu.phone,
            bu.is_proxy,
            bu.customer_type,
            bu.waiter_id,
            bu.status,
            bu.last_login_time,
            buc.register_time as create_time,
            bu.update_time,
            bu.account_type,
            buc.register_channel_type as channelType,
            buc.register_channel_id as channelId,
            bu.connect_user_name
        FROM biz_user_channel buc
        INNER JOIN biz_user bu ON buc.biz_user_id = bu.id
        WHERE buc.register_channel_type IN (${@<EMAIL>},
        ${@<EMAIL>}) -- DISTRIBUTION.getCode(), FISSION.getCode()
          AND buc.register_channel_id = #{distributionChannelId}
        ORDER BY bu.create_time DESC
    </select>
</mapper>
