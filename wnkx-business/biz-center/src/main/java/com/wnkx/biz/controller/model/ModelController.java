package com.wnkx.biz.controller.model;

import com.ruoyi.common.core.constant.UserTypeConstants;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.core.domain.vo.UserVO;
import com.ruoyi.common.core.utils.poi.ExcelUtil;
import com.ruoyi.common.core.validated.CommonValidatedGroup;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.page.PageInfo;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import com.ruoyi.common.security.annotation.InnerAuth;
import com.ruoyi.common.security.annotation.Logical;
import com.ruoyi.common.security.annotation.LoginUserType;
import com.ruoyi.common.security.annotation.RequiresPermissions;
import com.ruoyi.system.api.domain.dto.ModelListDTO;
import com.ruoyi.system.api.domain.dto.biz.business.BusinessAccountCollectModelDTO;
import com.ruoyi.system.api.domain.dto.biz.model.*;
import com.ruoyi.system.api.domain.entity.Model;
import com.ruoyi.system.api.domain.entity.ModelPerson;
import com.ruoyi.system.api.domain.entity.order.datastatistics.CustomerServiceAddedOustModelCountInfo;
import com.ruoyi.system.api.domain.vo.biz.business.BusinessAccountCollectModelVO;
import com.ruoyi.system.api.domain.vo.biz.model.*;
import com.ruoyi.system.api.domain.vo.order.datastatistics.EnglishCustomerServiceDataVO;
import com.wnkx.biz.business.service.IBusinessAccountModelLibraryService;
import com.wnkx.biz.model.service.IModelPersonService;
import com.wnkx.biz.model.service.IModelService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.Collection;
import java.util.List;

/**
 * 模特信息Controller
 *
 * <AUTHOR>
 * @date 2024-05-20
 */
@RestController
@RequestMapping("/model")
@Api(value = "模特信息服务", tags = "模特信息服务")
@Validated
@RequiredArgsConstructor
public class ModelController extends BaseController {
    private final IModelService modelService;
    private final IModelPersonService modelPersonService;
    private final IBusinessAccountModelLibraryService modelMerchantService;

    /**
     * 查询模特信息列表
     */
    @GetMapping("/list")
    @LoginUserType(userTypes = UserTypeConstants.MANAGER_TYPE)
    @ApiOperation(value = "查询模特信息列表", response = PageInfo.class)
    @RequiresPermissions("model:manage:list")
    public R<PageInfo<ModelVO>> list(@Validated ModelListDTO modelListDTO) {
        List<ModelVO> list = modelService.selectModelListByCondition(modelListDTO);
        return R.ok(toPage(list));
    }


    /**
     * 获取模特姓名和账户名组合列表（用于下拉筛选）
     */
    @GetMapping("/name-account-options")
    @LoginUserType(userTypes = UserTypeConstants.MANAGER_TYPE)
    @ApiOperation(value = "获取模特姓名和账户名组合列表", notes = "返回格式为'模特姓名+账户名'的字符串列表，用于前端下拉筛选", response = String.class)
    @RequiresPermissions("model:manage:list")
    public R<List<ModelNameAccountVO>> getModelNameAccountOptions() {
        List<ModelNameAccountVO> options = modelService.selectModelNameAccountOptions();
        return R.ok(options);
    }

    /**
     * 查询模特信息列表（无需登录）
     */
    @GetMapping("/reference-list")
    @ApiOperation(value = "查询模特信息列表（无需登录）", response = ModelListSimpleVO.class)
    public R<List<ModelListSimpleVO>> referenceList(BusinessAccountCollectModelDTO dto) {
        List<ModelListSimpleVO> list = modelService.referenceList(dto);
        return R.ok(list);
    }

    @GetMapping(value = "/referenceModelInfo/{id}")
    @ApiOperation(value = "查询模特信息详情（无需登录）", response = BusinessAccountCollectModelVO.class)
    public R<BusinessAccountCollectModelVO> getReferenceModelInfo(@ApiParam("模特id") @PathVariable Long id,
                                                                  @ApiParam("擅长品类") @RequestParam(required = false) List<Long> specialtyCategory) {
        BusinessAccountCollectModelVO info = modelMerchantService.getReferenceModelInfo(id, specialtyCategory);
        return R.ok(info);
    }

    /**
     * 导出模特信息列表
     */
    @Log(title = "模特信息", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    @ApiOperation(value = "导出模特信息列表")
    @LoginUserType(userTypes = UserTypeConstants.MANAGER_TYPE)
    @RequiresPermissions("model:manage:export")
    public void export(HttpServletResponse response, ModelListDTO modelListDTO) {
        List<ModelVO> list = modelService.selectModelListByCondition(modelListDTO);
        List<ModelExportDTO> export = modelService.getExportModel(list);
        ExcelUtil<ModelExportDTO> util = new ExcelUtil<>(ModelExportDTO.class);
        // 设置响应头信息
        ExcelUtil.setAttachmentResponseHeader(response, "模特信息数据");
        util.exportExcel(response, export, "模特信息数据");
    }

    /**
     * 获取模特信息详细信息
     */
    @GetMapping(value = "/{id}")
    @LoginUserType(userTypes = UserTypeConstants.MANAGER_TYPE)
    @ApiOperation(value = "获取模特信息详细信息", response = ModelVO.class)
    @RequiresPermissions(value = {
            "model:manage:detail",
            "model:manage:edit-modelPicTemp",
            "model:manage:edit-haveSnailPic",
            "model:manage:edit-name",
            "model:manage:edit-sex",
            "model:manage:edit-type",
            "model:manage:edit-ageGroup",
            "model:manage:edit-birthday",
            "model:manage:edit-about",
            "model:manage:edit-address",
            "model:manage:edit-specialtyCategory",
            "model:manage:edit-tags",
            "model:manage:edit-sort",
            "model:manage:edit-isShow",
            "model:manage:edit-platform",
            "model:manage:edit-cooperationScore",
            "model:manage:edit-persons",
            "model:manage:edit-developerId",
            "model:manage:edit-acceptability",
            "model:manage:edit-commission",
            "model:manage:edit-caseVideo",
            "model:manage:edit-lifePhoto",
            "model:manage:edit-label"
    }, logical = Logical.OR)
    public R<ModelVO> getInfo(@PathVariable("id") Long id) {
        return R.ok(modelService.selectModelById(id));
    }

    /**
     * 新增模特信息
     */
    @Log(title = "模特信息", businessType = BusinessType.INSERT)
    @PostMapping
    @LoginUserType(userTypes = UserTypeConstants.MANAGER_TYPE)
    @ApiOperation(value = "新增模特信息")
    @RequiresPermissions("model:manage:add")
    public R<String> add(@RequestBody @Validated(CommonValidatedGroup.SaveValidatedGroup.class) ModelDTO modelDTO) {
        modelService.insertModel(modelDTO);
        return R.ok();
    }

    /**
     * 修改模特信息
     */
    @Log(title = "模特信息", businessType = BusinessType.UPDATE)
    @PutMapping
    @LoginUserType(userTypes = UserTypeConstants.MANAGER_TYPE)
    @ApiOperation(value = "修改模特信息")
    @RequiresPermissions(value = {
            "model:manage:edit-modelPicTemp",
            "model:manage:edit-haveSnailPic",
            "model:manage:edit-name",
            "model:manage:edit-sex",
            "model:manage:edit-type",
            "model:manage:edit-ageGroup",
            "model:manage:edit-birthday",
            "model:manage:edit-about",
            "model:manage:edit-address",
            "model:manage:edit-specialtyCategory",
            "model:manage:edit-tags",
            "model:manage:edit-sort",
            "model:manage:edit-isShow",
            "model:manage:edit-platform",
            "model:manage:edit-cooperationScore",
            "model:manage:edit-persons",
            "model:manage:edit-developerId",
            "model:manage:edit-acceptability",
            "model:manage:edit-commission",
            "model:manage:edit-caseVideo",
            "model:manage:edit-lifePhoto",
            "model:manage:edit-label"
    }, logical = Logical.OR)
    public R<String> edit(@RequestBody @Validated(CommonValidatedGroup.EditValidatedGroup.class) ModelDTO modelDTO) {
        modelService.updateModel(modelDTO);
        return R.ok();
    }

    /**
     * 更新模特状态
     */
    @PutMapping("update-model-status")
    @LoginUserType(userTypes = UserTypeConstants.MANAGER_TYPE)
    @ApiOperation(value = "更新模特状态")
    @RequiresPermissions(value = {"model:manage:status-normal", "model:manage:status-journey", "model:manage:status-pause", "model:manage:status-cancel"}, logical = Logical.OR)
    public R<String> updateModelStatus(@RequestBody @Validated ModelUpdateStatusDTO dto) {
        modelService.updateModelStatus(dto);
        return R.ok();
    }

    @GetMapping("/getMsg/{modelId}")
    @LoginUserType(userTypes = {UserTypeConstants.MANAGER_TYPE, UserTypeConstants.USER_TYPE})
    @ApiOperation(value = "更新模特提示语")
    public R getMsg(@ApiParam("模特id") @PathVariable("modelId") String modelId) {
        return R.ok(modelService.getMsg(modelId));
    }

    /**
     * 置顶模特
     */
    @PutMapping("top/{id}")
    @LoginUserType(userTypes = UserTypeConstants.MANAGER_TYPE)
    @ApiOperation(value = "置顶模特")
    @RequiresPermissions("model:manage:top")
    public R<String> top(@PathVariable Long id) {
        modelService.top(id);
        return R.ok();
    }

    /**
     * 置顶模特数量统计
     */
    @GetMapping("top-count")
    @ApiOperation(value = "置顶模特数量统计")
    @LoginUserType(userTypes = UserTypeConstants.MANAGER_TYPE)
    @RequiresPermissions("model:manage:list")
    public R<ModelTopCountVO> topCount() {
        ModelTopCountVO modelTopCountVO = modelService.topCount();
        return R.ok(modelTopCountVO);
    }

    /**
     * 模特关联人员
     */
    @GetMapping("relevance/{id}")
    @ApiOperation(value = "模特关联人员")
    @LoginUserType(userTypes = UserTypeConstants.MANAGER_TYPE)
    @RequiresPermissions("model:manage:list")
    public R<List<UserVO>> relevance(@PathVariable Long id) {
        List<UserVO> relevance = modelService.relevance(id);
        return R.ok(relevance);
    }

    /**
     * 批量修改对接人
     */
    @PostMapping("update-relevance")
    @LoginUserType(userTypes = UserTypeConstants.MANAGER_TYPE)
    @ApiOperation(value = "批量修改对接人")
    @RequiresPermissions("model:manage:batchChangeConectUser")
    public R<String> updateRelevance(@RequestBody @Validated ModelRelevanceDTO modelRelevanceDTO) {
        modelService.updateRelevance(modelRelevanceDTO);
        return R.ok();
    }

    /**
     * 获取模特后台链接
     */
    @PostMapping("background-link/{id}")
    @LoginUserType(userTypes = UserTypeConstants.MANAGER_TYPE)
    @ApiOperation(value = "获取模特后台链接")
    @RequiresPermissions("model:manage:link")
    public R<String> backgroundLink(@PathVariable Long id) {
        return R.ok(modelService.backgroundLink(id));
    }


    /**
     * 查询模特信息列表（内部请求）
     */
    @PostMapping("/inner-list")
    @InnerAuth
    public R<List<ModelInfoVO>> innerList(@RequestBody ModelListDTO modelListDTO) {
        List<ModelInfoVO> list = modelService.innerList(modelListDTO);
        return R.ok(list);
    }

    /**
     * 查询模特简单信息（用于订单列表模特数据）
     */
    @PostMapping("/query-model-simple-list")
    @InnerAuth
    public R<List<ModelOrderSimpleVO>> queryModelSimpleList(@RequestBody ModelListDTO modelListDTO) {
        List<ModelOrderSimpleVO> list = modelService.queryModelSimpleList(modelListDTO);
        return R.ok(list);
    }

    /**
     * 模糊查询模特信息列表（模特名称、模特账号）
     */
    @PostMapping("/query-like-model-list")
    @InnerAuth
    public R<List<ModelInfoVO>> queryLikeModelList(@RequestBody ModelListDTO modelListDTO) {
        List<ModelInfoVO> list = modelService.queryLikeModelList(modelListDTO);
        return R.ok(list);
    }


    /**
     * 查询不可接单的模特（内部请求）
     */
    @PostMapping("/query-cannot-accept-list")
    @InnerAuth
    public R<List<Model>> queryCannotAcceptList(@RequestBody CannotAcceptModelDTO dto) {
        List<Model> list = modelService.queryCannotAcceptList(dto);
        return R.ok(list);
    }

    @PostMapping("/queryBlacklist")
    @InnerAuth
    public R<List<Model>> queryBlacklist(@RequestBody CannotAcceptModelDTO dto) {
        List<Model> list = modelService.queryCannotAcceptList(dto);
        return R.ok(list);
    }


    /**
     * 模特列表下拉框
     */
    @GetMapping("/select-list")
    @ApiOperation(value = "模特列表下拉框", response = PageInfo.class)
    @LoginUserType(userTypes = UserTypeConstants.MANAGER_TYPE)
    public R<PageInfo<ModelVO>> selectList(ModelListDTO modelListDTO) {
        startPage();
        List<ModelVO> list = modelService.selectList(modelListDTO);
        return R.ok(toPage(list));
    }

    /**
     * 查询模特关联运营
     */
    @PostMapping("/query-model-person")
    @InnerAuth
    public R<List<ModelPerson>> queryModelPerson(@RequestBody Collection<Long> modelIds) {
        List<ModelPerson> modelPeople = modelService.queryModelPerson(modelIds);
        return R.ok(modelPeople);
    }

    /**
     * 添加预选模特列表
     */
    @GetMapping(value = "/add-preselect-model-list")
    @ApiOperation(value = "添加预选模特列表")
    @LoginUserType(userTypes = {UserTypeConstants.MANAGER_TYPE})
    @RequiresPermissions(value = {"preselection:order:add", "my:preselection:add"}, logical = Logical.OR)
    public R<PageInfo<AddPreselectModelListVO>> addPreselectModelList(@Validated AddPreselectModelListDTO addPreselectModelListDTO) {
        PreselectModelListResultVO result = modelService.addPreselectModelList(addPreselectModelListDTO);
        return R.ok(toPage(result.getList()),result.getIsLimitedToQualityModel().toString());
    }


    /**
     * 模特列表-获取关联人员下拉框（运营端）
     */
    @GetMapping("/model-persons-select")
    @LoginUserType(userTypes = UserTypeConstants.MANAGER_TYPE)
    @ApiOperation("模特列表-获取关联人员下拉框（运营端）")
    @RequiresPermissions("model:manage:list")
    public R<List<UserVO>> modelPersonsSelect(@RequestParam(required = false) String keyword) {
        List<UserVO> select = modelService.modelPersonsSelect(keyword);
        return R.ok(select);
    }

    /**
     * 运营端-编辑订单-更换模特列表
     */
    @GetMapping("/edit-order-change-model-list")
    @LoginUserType(userTypes = UserTypeConstants.MANAGER_TYPE)
    @ApiOperation(value = "运营端-编辑订单-更换模特列表", response = ModelVO.class)
    public R<PageInfo<ModelVO>> editOrderChangeModelList(@Validated EditOrderChangeModelListDTO dto) {
        List<ModelVO> list = modelService.editOrderChangeModelList(dto);
        return R.ok(toPage(list));
    }

    /**
     * 查询模特信息变更记录
     */
    @GetMapping("/model-change-record/{modelId}")
    @ApiOperation(value = "查询模特信息变更记录")
    @LoginUserType(userTypes = UserTypeConstants.MANAGER_TYPE)
    @RequiresPermissions("model:manage:list")
    public R<List<ModelChangeRecordVO>> modelChangeRecord(@PathVariable Long modelId) {
        List<ModelChangeRecordVO> list = modelService.modelChangeRecord(modelId);
        return R.ok(list);
    }

    /**
     * 查询模特信息 用于记录视频订单模特变更记录
     */
    @PostMapping("/select-model-change-list")
    @InnerAuth
    public R<List<ModelChangeVO>> selectModelChangeList(@RequestBody ModelListDTO modelListDTO) {
        List<ModelChangeVO> list = modelService.selectModelChangeList(modelListDTO);
        return R.ok(list);
    }

    /**
     * 修改排序
     */
    @PutMapping("/update-sort")
    @ApiOperation(value = "修改排序")
    @LoginUserType(userTypes = UserTypeConstants.MANAGER_TYPE)
    @RequiresPermissions("model:manage:sort")
    public R<String> updateSort(@RequestBody @Validated ModelSortDTO modelSortDTO) {
        modelService.updateSort(modelSortDTO);
        return R.ok();
    }

    /**
     * 查询模特信息（预选模特对象）
     */
    @PostMapping("/select-model-info-of-preselection")
    @InnerAuth
    public R<List<AddPreselectModelListVO>> selectModelInfoOfPreselection(@RequestBody ModelListDTO modelListDTO) {
        List<AddPreselectModelListVO> list = modelService.selectModelInfoOfPreselection(modelListDTO);
        return R.ok(list);
    }

    /**
     * 查询当前运营关联的模特
     */
    @PostMapping("/select-model-person-by-user-ids")
    @InnerAuth
    public R<List<ModelPerson>> selectModelPersonByUserIds(@RequestBody Collection<Long> userIds) {
        List<ModelPerson> list = modelPersonService.selectListByUserIds(userIds);
        return R.ok(list);
    }

    @GetMapping("/family/list")
    @LoginUserType(userTypes = UserTypeConstants.MANAGER_TYPE)
    @ApiOperation(value = "查询模特家庭列表", response = PageInfo.class)
    @RequiresPermissions("model:manage:family")
    public R<PageInfo<ModelVO>> familyList(@Validated ModelListDTO modelListDTO) {
        List<ModelVO> list = modelService.selectModelFamilyListByCondition(modelListDTO);
        return R.ok(toPage(list));
    }

    @PostMapping("/family/addFamilyModel")
    @LoginUserType(userTypes = UserTypeConstants.MANAGER_TYPE)
    @ApiOperation(value = "添加家庭成员")
    @RequiresPermissions("model:manage:family")
    public R<String> addFamilyModel(@RequestBody @Validated ModelFamilyInsertDTO modelDTO) {
        modelService.addFamilyModel(modelDTO);
        return R.ok();
    }

    @DeleteMapping("/family/deleteFamilyModel")
    @LoginUserType(userTypes = UserTypeConstants.MANAGER_TYPE)
    @ApiOperation(value = "删除家庭成员")
    @RequiresPermissions("model:manage:family")
    public R<String> deleteFamilyModel(@RequestBody @Validated ModelFamilyDeleteDTO modelDTO) {
        modelService.deleteFamilyModel(modelDTO);
        return R.ok();
    }

    /**
     * 获取英文部客服 新增/淘汰模特数
     */
    @GetMapping("/get-english-customer-service-added-oust-model-counts")
    @InnerAuth
    public R<List<CustomerServiceAddedOustModelCountInfo>> getEnglishCustomerServiceAddedOustModelCounts(@RequestParam String date) {
        List<CustomerServiceAddedOustModelCountInfo> list = modelService.getEnglishCustomerServiceAddedOustModelCounts(date);
        return R.ok(list);
    }

    /**
     * 客服数据-获取英文部关联模特数据
     */
    @GetMapping("/select-english-customer-service--model-data")
    @InnerAuth
    public R<List<EnglishCustomerServiceDataVO>> selectEnglishCustomerServiceModelData() {
        List<EnglishCustomerServiceDataVO> list = modelService.selectEnglishCustomerServiceModelData();
        return R.ok(list);
    }
}