package com.wnkx.biz.controller.channel;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import com.ruoyi.common.core.constant.UserTypeConstants;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.core.domain.dto.OrderByDto;
import com.ruoyi.common.core.utils.PageUtils;
import com.ruoyi.common.core.utils.poi.ExcelUtil;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.page.PageInfo;
import com.ruoyi.common.security.annotation.InnerAuth;
import com.ruoyi.common.security.annotation.Logical;
import com.ruoyi.common.security.annotation.LoginUserType;
import com.ruoyi.common.security.annotation.RequiresPermissions;
import com.ruoyi.common.security.utils.SecurityUtils;
import com.ruoyi.system.api.domain.dto.order.MemberChannelStatisticDTO;
import com.ruoyi.system.api.domain.dto.order.OrderMemberChannelListDTO;
import com.ruoyi.system.api.domain.dto.order.OrderMemberChannelSettlementDTO;
import com.ruoyi.system.api.domain.entity.order.OrderMemberChannel;
import com.ruoyi.system.api.domain.vo.order.*;
import com.wnkx.biz.channel.service.IOrderMemberChannelService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @program :woniu-world
 * @description :
 * @create :2024-09-30 17:18
 **/
@RestController
@RequestMapping("/member-channel")
@Api(value = "会员渠道记录", tags = "会员渠道记录信息服务")
@RequiredArgsConstructor
public class OrderMemberChannelController extends BaseController {

    private final IOrderMemberChannelService orderMemberChannelService;

    /**
     * 分销渠道结算-分销渠道列表
     */
    @GetMapping("/member-channel-list")
    @LoginUserType(userTypes = UserTypeConstants.MANAGER_TYPE)
    @ApiOperation(value = "分销渠道结算-分销渠道列表", response = PageInfo.class)
    @RequiresPermissions(value = {"distribution:channel:records", "channel:distribution:detail"}, logical = Logical.OR)
    public R<PageInfo<OrderMemberChannelListVO>> memberChannelListByCondition(OrderMemberChannelListDTO dto) {
        List<OrderMemberChannelListVO> list = orderMemberChannelService.memberChannelListByCondition(dto);
        return R.ok(toPage(list));
    }
    /**
     * 分销渠道结算-分销渠道列表
     */
    @GetMapping("/old/member-channel-list")
    @LoginUserType(userTypes = UserTypeConstants.MANAGER_TYPE)
    @ApiOperation(value = "分销渠道结算-分销渠道列表", response = PageInfo.class)
    @RequiresPermissions(value = {"distribution:channel:records", "channel:distribution:detail"}, logical = Logical.OR)
    public R<PageInfo<OrderMemberChannelListVO>> oldMemberChannelListByCondition(OrderMemberChannelListDTO dto) {
        List<OrderMemberChannelListVO> list = orderMemberChannelService.oldMemberChannelListByCondition(dto);
        return R.ok(toPage(list));
    }

    @PostMapping("/member-channel-list/export")
    @LoginUserType(userTypes = UserTypeConstants.MANAGER_TYPE)
    @ApiOperation(value = "分销渠道结算-导出")
    public void memberChannelListByConditionExport(OrderMemberChannelListDTO dto, HttpServletResponse response) {
        List<OrderMemberChannelListVO> list = orderMemberChannelService.memberChannelListByCondition(dto);
        ExcelUtil<OrderMemberChannelListExportVO> util = new ExcelUtil<>(OrderMemberChannelListExportVO.class);
        List<OrderMemberChannelListExportVO> orderMemberChannelListExportVOS = BeanUtil.copyToList(list, OrderMemberChannelListExportVO.class);
        // 处理字段格式化：结算比例和实际结算金额
        orderMemberChannelListExportVOS.forEach(exportVO -> {
            // 从原始VO中获取对应的数据进行格式化
            list.stream()
                .filter(vo -> vo.getId().equals(exportVO.getId()))
                .findFirst()
                .ifPresent(vo -> {
                    // 处理结算比例格式化：当settleType=2时添加百分号
                    if (exportVO.getSettleRageFormatted() == null && exportVO.getSettleType() != null) {
                        if (vo.getSettleRage() != null) {
                            // 当结算类型为2（固定比例）时，添加百分号
                            if (exportVO.getSettleType().equals(2)) {
                                exportVO.setSettleRageFormatted(vo.getSettleRage() + "%");
                            } else {
                                exportVO.setSettleRageFormatted(vo.getSettleRage().toString());
                            }
                        } else {
                            exportVO.setSettleRageFormatted("-");
                        }
                    }

                    // 处理实际结算金额格式化：当settleStatus!=4时显示"-"
                    if (exportVO.getRealSettleAmountFormatted() == null) {
                        if (exportVO.getSettleStatus() != null && exportVO.getSettleStatus().equals(4)) {
                            // 状态为4（已打款）时，显示实际金额
                            if (vo.getRealSettleAmount() != null) {
                                exportVO.setRealSettleAmountFormatted(vo.getRealSettleAmount().toString());
                            } else {
                                exportVO.setRealSettleAmountFormatted("-");
                            }
                        } else {
                            // 状态不为4时，显示"-"
                            exportVO.setRealSettleAmountFormatted("-");
                        }
                    }
                });
        });

        ExcelUtil.setAttachmentResponseHeader(response, "分销渠道结算导出");
        util.exportExcel(response, orderMemberChannelListExportVOS, "分销渠道结算导出");
    }


    @GetMapping("/fission/member-channel-list")
    @LoginUserType(userTypes = UserTypeConstants.MANAGER_TYPE)
    @ApiOperation(value = "裂变渠道结算-裂变渠道列表", response = PageInfo.class)
    public R<PageInfo<OrderMemberFissionListVO>> fissionMemberChannelListByCondition(OrderMemberChannelListDTO dto) {
        PageInfo<OrderMemberFissionListVO> orderMemberFissionListVOPageInfo = orderMemberChannelService.fissionMemberChannelListByCondition(dto);
        return R.ok(orderMemberFissionListVOPageInfo);
    }

    @PostMapping("/fission/member-channel-list/export")
    @LoginUserType(userTypes = UserTypeConstants.MANAGER_TYPE)
    @ApiOperation(value = "裂变渠道结算-导出")
    public void fissionMemberChannelListByConditionExport(OrderMemberChannelListDTO dto, HttpServletResponse response) {
        PageInfo<OrderMemberFissionListVO> orderMemberFissionListVOPageInfo = orderMemberChannelService.fissionMemberChannelListByCondition(dto);
        List<OrderMemberFissionListVO> list = orderMemberFissionListVOPageInfo.getRows();
        ExcelUtil<OrderMemberFissionListExportVO> util = new ExcelUtil<>(OrderMemberFissionListExportVO.class);
        if (CollectionUtil.isEmpty(list)) {
            return;
        }
        List<OrderMemberFissionListExportVO> orderMemberFissionListExportVOS = BeanUtil.copyToList(list, OrderMemberFissionListExportVO.class);
        // 处理字段格式化：结算比例和实际结算金额
        orderMemberFissionListExportVOS.forEach(exportVO -> {
            // 从原始VO中获取对应的数据进行格式化
            list.stream()
                .filter(vo -> vo.getId().equals(exportVO.getId()))
                .findFirst()
                .ifPresent(vo -> {
                    // 处理结算比例格式化：当settleType=2时添加百分号
                    if (exportVO.getSettleRageFormatted() == null && exportVO.getSettleType() != null) {
                        if (vo.getSettleRage() != null) {
                            // 当结算类型为2（固定比例）时，添加百分号
                            if (exportVO.getSettleType().equals(2)) {
                                exportVO.setSettleRageFormatted(vo.getSettleRage() + "%");
                            } else {
                                exportVO.setSettleRageFormatted(vo.getSettleRage().toString());
                            }
                        } else {
                            exportVO.setSettleRageFormatted("-");
                        }
                    }

                    // 处理实际结算金额格式化：当settleStatus!=4时显示"-"
                    if (exportVO.getRealSettleAmountFormatted() == null) {
                        if (exportVO.getSettleStatus() != null && exportVO.getSettleStatus().equals(4)) {
                            // 状态为4（已打款）时，显示实际金额
                            if (vo.getRealSettleAmount() != null) {
                                exportVO.setRealSettleAmountFormatted(vo.getRealSettleAmount().toString());
                            } else {
                                exportVO.setRealSettleAmountFormatted("-");
                            }
                        } else {
                            // 状态不为4时，显示"-"
                            exportVO.setRealSettleAmountFormatted("-");
                        }
                    }
                });
        });

        ExcelUtil.setAttachmentResponseHeader(response, "裂变渠道结算导出");
        util.exportExcel(response, orderMemberFissionListExportVOS, "裂变渠道结算导出");
    }

    @GetMapping("/member-channel-detail/{id}")
    @LoginUserType(userTypes = UserTypeConstants.MANAGER_TYPE)
    @ApiOperation(value = "分销渠道结算-查看结算记录", response = OrderMemberChannelDetailVO.class)
    @RequiresPermissions("distribution:channel:records")
    public R<OrderMemberChannelDetailVO> memberChannelDetailById(@PathVariable Long id) {
        OrderMemberChannelDetailVO result = orderMemberChannelService.memberChannelDetailById(id);
        return R.ok(result);
    }

    @GetMapping("/fission/member-channel-detail/{id}")
    @LoginUserType(userTypes = UserTypeConstants.MANAGER_TYPE)
    @ApiOperation(value = "裂变渠道结算-查看结算记录", response = OrderMemberChannelDetailVO.class)
    public R<OrderMemberChannelDetailVO> fissionMemberChannelDetailById(@PathVariable Long id) {
        OrderMemberChannelDetailVO result = orderMemberChannelService.memberChannelDetailById(id);
        return R.ok(result);
    }

    /**
     * 分销渠道结算-结算
     */
    @PostMapping("/member-channel-settlement")
    @LoginUserType(userTypes = UserTypeConstants.MANAGER_TYPE)
    @ApiOperation(value = "分销渠道结算-结算")
    @RequiresPermissions("distribution:channel:settlement")
    public R<String> memberChannelSettlement(@RequestBody @Validated OrderMemberChannelSettlementDTO dto) {
        orderMemberChannelService.memberChannelSettlement(dto);
        return R.ok();
    }

    @PostMapping("/fission/member-channel-settlement")
    @LoginUserType(userTypes = UserTypeConstants.MANAGER_TYPE)
    @ApiOperation(value = "分销渠道结算-结算")
    public R<String> fissionMemberChannelSettlement(@RequestBody @Validated OrderMemberChannelSettlementDTO dto) {
        orderMemberChannelService.memberChannelSettlement(dto);
        return R.ok();
    }

    @PostMapping("/getMemberChannelStatistic")
    @ApiOperation(value = "获取各个分销渠道统计")
    public R<Map<Long, OrderMemberChannelStatisticVO>> getMemberChannelStatistic(@RequestBody @Validated MemberChannelStatisticDTO dto) {
        return R.ok(orderMemberChannelService.getMemberChannelStatistic(dto.getChannelIds()));
    }

    @GetMapping("/channel/memberChannelList")
    @ApiOperation(value = "获取渠道记录（渠道端）", response = PageInfo.class)
    @LoginUserType(userTypes = UserTypeConstants.CHANNEL_TYPE)
    public R<PageInfo<OrderMemberChannel>> memberChannelList() {
        OrderByDto orderByDto = new OrderByDto();
        orderByDto.setField("create_time", OrderByDto.DIRECTION.DESC);
        PageUtils.startPage(orderByDto);
        return R.ok(toPage(orderMemberChannelService.memberChannelListByChannelIds(Arrays.asList(SecurityUtils.getUserId()))));
    }

    /**
     * 分销渠道结算-分销渠道列表
     */
    @PostMapping("/inner/member-channel-list")
    @InnerAuth
    public R<List<OrderMemberChannelListVO>> innerMemberChannelListByCondition(@RequestBody OrderMemberChannelListDTO dto) {
        return R.ok(orderMemberChannelService.memberChannelListByCondition(dto));
    }

}
