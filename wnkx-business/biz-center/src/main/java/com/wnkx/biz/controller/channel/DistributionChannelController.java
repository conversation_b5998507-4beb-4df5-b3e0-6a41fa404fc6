package com.wnkx.biz.controller.channel;

import com.ruoyi.common.core.constant.UserTypeConstants;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.core.domain.dto.OrderByDto;
import com.ruoyi.common.core.enums.ChannelTypeEnum;
import com.ruoyi.common.core.utils.PageUtils;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.page.PageInfo;
import com.ruoyi.common.security.annotation.InnerAuth;
import com.ruoyi.common.security.annotation.Logical;
import com.ruoyi.common.security.annotation.LoginUserType;
import com.ruoyi.common.security.annotation.RequiresPermissions;
import com.ruoyi.system.api.domain.dto.biz.business.account.user.BizUserListDTO;
import com.ruoyi.system.api.domain.dto.biz.channel.*;
import com.ruoyi.system.api.domain.dto.system.EditMemberDiscountDTO;
import com.ruoyi.system.api.domain.entity.biz.channel.DistributionChannel;
import com.ruoyi.system.api.domain.entity.biz.channel.DistributionChannelDiscountLog;
import com.ruoyi.system.api.domain.vo.SysUserVO;
import com.ruoyi.system.api.domain.vo.biz.business.user.BizUserListVO;
import com.ruoyi.system.api.domain.vo.biz.channel.*;
import com.ruoyi.system.api.domain.vo.biz.channel.ChannelBrokeRageVO;
import com.wnkx.biz.business.service.IBusinessAccountService;
import com.wnkx.biz.channel.service.DistributionChannelActivityService;
import com.wnkx.biz.channel.service.DistributionChannelDiscountLogService;
import com.wnkx.biz.channel.service.IDistributionChannelService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.Collection;
import java.util.List;

/**
 * <AUTHOR>
 * @program :woniu-world
 * @description :
 * @create :2024-07-10 13:52
 **/
@RestController
@RequestMapping("/marketing/center/distribution")
@Api(value = "营销中心-分销渠道", tags = "营销中心-分销渠道")
@RequiredArgsConstructor
public class DistributionChannelController extends BaseController {
    private final IDistributionChannelService distributionChannelService;
    private final DistributionChannelActivityService distributionChannelActivityService;
    private final IBusinessAccountService businessAccountService;
    private final DistributionChannelDiscountLogService distributionChannelDiscountLogService;

    @ApiOperation("新增分销渠道")
    @LoginUserType(userTypes = UserTypeConstants.MANAGER_TYPE)
    @RequiresPermissions("channel:distribution:add")
    @PostMapping("/backend/saveDistribution")
    public R<SaveDistributionChannelVO> saveDistribution(@RequestBody @Validated DistributionChancelSaveDTO dto) {
        return R.ok(distributionChannelService.saveDistribution(dto));
    }

    @ApiOperation("修改分销渠道")
    @LoginUserType(userTypes = UserTypeConstants.MANAGER_TYPE)
    @RequiresPermissions("channel:distribution:edit")
    @PutMapping("/backend/editDistribution")
    public R<String> editDistribution(@RequestBody @Validated DistributionChancelEditDTO dto) {
        distributionChannelService.editDistribution(dto);
        return R.ok();
    }

    @GetMapping("/getDistributionChannelDiscountLogList")
    @LoginUserType(userTypes = UserTypeConstants.MANAGER_TYPE)
    @ApiOperation(value = "获取结算折扣日志", response = PageInfo.class)
    public R<PageInfo<DistributionChannelDiscountLog>> getDistributionChannelDiscountLogList(@RequestParam Long channelId) {
        OrderByDto orderByDto = new OrderByDto();
        orderByDto.setField("create_time" , OrderByDto.DIRECTION.DESC);
        PageUtils.startPage(orderByDto);
        return R.ok(toPage(distributionChannelDiscountLogService.queryListByChannelId(channelId)));
    }


    @PutMapping("/editMemberDiscount")
    @LoginUserType(userTypes = UserTypeConstants.MANAGER_TYPE)
    @RequiresPermissions("system:config:editMemberDiscount")
    @ApiOperation(value = "修改会员折扣", response = PageInfo.class)
    public R<String> editMemberDiscount(@Validated @RequestBody EditMemberDiscountDTO dto) {
        dto.setChannelType(ChannelTypeEnum.DISTRIBUTION.getCode());
        distributionChannelService.editMemberDiscount(dto);
        return R.ok();
    }

    @ApiOperation("修改分销渠道状态")
    @LoginUserType(userTypes = UserTypeConstants.MANAGER_TYPE)
    @RequiresPermissions("channel:distribution:status")
    @PutMapping("/backend/updateStatus")
    public R<String> updateStatus(@RequestBody @Validated DistributionStatusDTO dto) {
        distributionChannelService.updateStatus(dto);
        return R.ok();
    }


    @GetMapping("/backend/list")
    @LoginUserType(userTypes = {UserTypeConstants.MANAGER_TYPE})
    @ApiOperation(value = "分销渠道列表", response = PageInfo.class)
    @RequiresPermissions("channel:distribution:list")
    public R<PageInfo<DistributionChannelVO>> videoList(DistributionChannelListDTO dto) {
        dto.setChannelType(ChannelTypeEnum.DISTRIBUTION.getCode());
        return R.ok(toPage(distributionChannelService.queryList(dto)));
    }

    @GetMapping("/backend/list/statistics")
    @LoginUserType(userTypes = {UserTypeConstants.MANAGER_TYPE})
    @ApiOperation(value = "分销渠道列表统计")
    @RequiresPermissions("channel:distribution:list")
    public R<DistributionChannelStatisticsVO> videoListStatistics(DistributionChannelListDTO dto) {
        dto.setChannelType(ChannelTypeEnum.DISTRIBUTION.getCode());
        return R.ok(distributionChannelService.statistics(dto));
    }

    @GetMapping("/backend/{id}")
    @LoginUserType(userTypes = {UserTypeConstants.MANAGER_TYPE})
    @ApiOperation(value = "分销渠道详情", response = DistributionChannelDetailVO.class)
    @RequiresPermissions(value = {"channel:distribution:edit", "channel:distribution:detail"}, logical = Logical.OR)
    public R<DistributionChannelDetailVO> getByDistributionChannelId(@PathVariable Long id) {
        return R.ok(distributionChannelService.getByDistributionChannelId(id));
    }


    @GetMapping("/backend/statistics")
    @LoginUserType(userTypes = {UserTypeConstants.MANAGER_TYPE})
    @ApiOperation(value = "分销渠道统计", response = DistributionChannelStatisticsVO.class)
    @RequiresPermissions("channel:distribution:list")
    public R<DistributionChannelStatisticsVO> statistics(DistributionChancelStatisticsDTO dto) {
        dto.setChannelType(ChannelTypeEnum.DISTRIBUTION.getCode());
        return R.ok(distributionChannelService.statistics(dto));
    }

    /**
     * 根据种草码 获取分销渠道信息
     */
    @GetMapping("/backend/getFissionBrokeRageBySeedCode")
    @InnerAuth
    public R<ChannelBrokeRageVO> getFissionBrokeRageBySeedCode(@RequestParam String seedCode) {
        return R.ok(distributionChannelActivityService.getChannelDiscount(seedCode));
    }

    @Deprecated(since = "2025-01-14", forRemoval = true)
    @PostMapping("/inner/queryDistributionChannelsBySeedCodes")
    @InnerAuth
    public R<List<DistributionChannel>> queryDistributionChannelsBySeedCodes(@RequestBody Collection<String> seedCodes) {
        return R.ok(distributionChannelService.queryListBySeedCodes(seedCodes));
    }

    /**
     * 根据种草码 获取分销渠道信息
     */
    @GetMapping("/get-by-seed-code")
    @InnerAuth
    public R<DistributionChannel> getDistributionChannelEntityBySeedCode(@RequestParam String seedCode) {
        return R.ok(distributionChannelService.getDistributionChannelEntityBySeedCode(seedCode));
    }

    @PostMapping("/saveDistributionChannelOrder")
    @InnerAuth
    public R<String> saveDistributionChannelOrder(@RequestBody @Validated DistributionChannelOrderDTO dto) {
        distributionChannelService.saveDistributionChannelOrder(dto);
        return R.ok();
    }

    @Deprecated(since = "2025-01-14", forRemoval = true)
    @PostMapping("/saveBatchDistributionChannelOrder")
    @InnerAuth
    public R<String> saveBatchDistributionChannelOrder(@RequestBody @Validated DistributionChannelOrderBatchDTO dto) {
        distributionChannelService.saveBatchDistributionChannelOrder(dto);
        return R.ok();
    }

    @GetMapping("/backend/inviteList")
    @LoginUserType(userTypes = {UserTypeConstants.MANAGER_TYPE})
    @ApiOperation(value = "邀请记录列表", response = PageInfo.class)
    @RequiresPermissions("channel:distribution:detail")
    public R<PageInfo<ChannelInviteVO>> inviteList(@Validated InviteListDTO dto) {
        dto.setChannelType(ChannelTypeEnum.DISTRIBUTION.getCode());
        return R.ok(toPage(distributionChannelService.inviteList(dto)));
    }


    /**
     * 获取渠道私密信息*
     *
     * @param id
     * @return
     */
    @GetMapping("/backend/privacy/{id}")
    @LoginUserType(userTypes = {UserTypeConstants.MANAGER_TYPE})
    @ApiOperation(value = "获取私密信息")
    @RequiresPermissions(value = {"channel:distribution:edit", "channel:distribution:copy"}, logical = Logical.OR)
    public R<ChannelPrivacyInfoVO> privacyInfo(@PathVariable Long id) {
        return R.ok(distributionChannelService.privacyInfo(id));
    }


    @GetMapping("/channelInfo")
    @LoginUserType(userTypes = {UserTypeConstants.CHANNEL_TYPE})
    @ApiOperation(value = "分销渠道详情（渠道端）", response = DistributionChannelDetailVO.class)
    public R<DistributionChannelInfoVO> channelInfo() {
        return R.ok(distributionChannelService.channelInfo());
    }

    @GetMapping("/channelRegisterList")
    @LoginUserType(userTypes = {UserTypeConstants.CHANNEL_TYPE})
    @ApiOperation(value = "渠道邀请列表（渠道端）", response = DistributionChannelDetailVO.class)
    public R<PageInfo<ChannelInviteVO>> channelRegisterList() {
        return R.ok(toPage(distributionChannelService.channelRegisterList()));
    }

    /**
     * 预览分销渠道海报
     */
    @GetMapping("/preview/poster/{id}")
    @LoginUserType(userTypes = {UserTypeConstants.MANAGER_TYPE})
    @ApiOperation(value = "预览分销渠道海报")
    public void previewPoster(@PathVariable Long id, HttpServletResponse response) {
        distributionChannelService.previewPoster(id, response);
    }

    /**
     * 下载分销渠道海报
     */
    @PostMapping("/download/poster/{id}")
    @LoginUserType(userTypes = {UserTypeConstants.MANAGER_TYPE})
    @ApiOperation(value = "下载分销渠道海报")
    @RequiresPermissions("channel:distribution:download")
    public void downloadPoster(@PathVariable Long id, HttpServletResponse response) {
        distributionChannelService.downloadManagerPoster(id, response);
    }

    /**
     * 下载分销渠道海报
     */
    @PostMapping("/channel/download/poster/{id}")
    @LoginUserType(userTypes = {UserTypeConstants.CHANNEL_TYPE})
    @ApiOperation(value = "下载分销渠道海报(渠道端)")
    public void channelDownloadPoster(@PathVariable Long id, HttpServletResponse response) {
        distributionChannelService.downloadPoster(id, response);
    }

    /**
     * 下载全部分销渠道海报
     */
    @PostMapping("/download/poster/all")
    @LoginUserType(userTypes = {UserTypeConstants.MANAGER_TYPE})
    @ApiOperation(value = "下载全部分销渠道海报")
    @RequiresPermissions("channel:distribution:download:all")
    public void downloadAllPoster(HttpServletResponse response) {
        distributionChannelService.downloadAllPoster(response);
    }

    @GetMapping("/createUserList")
    @LoginUserType(userTypes = UserTypeConstants.MANAGER_TYPE)
    @ApiOperation(value = "获取分销渠道创建人列表", response = PageInfo.class)
    public R<PageInfo<SysUserVO>> createUserList(String username) {
        return R.ok(toPage(distributionChannelService.createUserList(username)));
    }


    @ApiOperation(value = "查看账号信息", response = PageInfo.class)
    @LoginUserType(userTypes = UserTypeConstants.MANAGER_TYPE)
    @GetMapping("/backend/bizUserList")
    @RequiresPermissions("channel:distribution:account")
    public R<PageInfo<BizUserListVO>> bizUserList(BizUserListDTO dto) {
        return R.ok(toPage(businessAccountService.bizUserList(dto)));
    }

    @ApiOperation(value = "查看邀请注册记录", response = PageInfo.class)
    @LoginUserType(userTypes = UserTypeConstants.CHANNEL_TYPE)
    @GetMapping("/backend/inviteRegisterList")
    public R<PageInfo<BizUserListVO>> inviteRegisterList() {
        return R.ok(toPage(distributionChannelService.inviteRegisterList()));
    }


}
